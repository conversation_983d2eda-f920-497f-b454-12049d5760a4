#coding:gbk

"""
========================================================================
策略名称: CMF+BIAS双重背离策略实盘版 - 基于K线合成的多重背离确认系统
策略类型: 多重背离确认策略
适用环境: QMT实盘交易
风险等级: 中等风险
版本信息: V3.0 CMF+BIAS双重背离版
========================================================================

⚠️ 重要风险提示:
1. 本策略仅供学习和研究使用，实盘交易存在亏损风险
2. 请在充分理解策略逻辑后谨慎使用
3. 建议先在模拟环境中测试验证
4. 实盘使用前请根据个人风险承受能力调整参数
5. 策略不保证盈利，过往表现不代表未来收益

策略说明:
- K线合成: 2根K线合成1根合成K线（非重叠模式）
- 四层过滤架构: 背离识别 → 市场环境过滤 → 精准入场时机 → 动态风险管理
- 核心算法:
  * 第一层: CMF资金流背离 + BIAS乖离率背离双重确认
  * 第二层: SKDJ超卖确认(K<20且D<20) + ADX强趋势确认(ADX>40)
  * 第三层: 阻力线突破确认 = 收盘价突破阻力线（简化版）
  * 第四层: VAE动态风控 + 0.5%固定止损
- 开仓条件: 4个条件同时满足 (SKDJ超卖 + 双重背离 + 强趋势 + 突破确认)
- 平仓条件: VAE动态止盈 或 VAE动态止损 或 0.5%固定止损
========================================================================
"""

# 导入必要的库
import numpy as np
import pandas as pd
import talib
import datetime
import traceback

# ============================================================================
# CMF+BIAS双重背离策略检测器类
# ============================================================================

class CMFBIASDivergenceDetector:
    """
    CMF+BIAS双重背离检测器
    基于合成K线数据进行多重背离信号识别
    """

    def __init__(self,
                 SKDJ_N=8, SKDJ_M=4,
                 CMF_N=25, CMF_M=18,
                 BIAS_N=25, BIAS_M=18,
                 ADX_N=20, ADX_M=10,
                 VAE_基础TR=1.8, VAE_初始止损=1.5, VAE_周期=17,
                 固定止损=0.5):
        """
        初始化CMF+BIAS双重背离检测器

        参数:
            SKDJ_N: SKDJ计算周期 (默认8)
            SKDJ_M: SKDJ平滑周期 (默认4)
            CMF_N: CMF计算周期 (默认30)
            CMF_M: CMF背离判断周期 (默认20)
            BIAS_N: BIAS计算周期 (默认30)
            BIAS_M: BIAS背离判断周期 (默认20)
            ADX_N: ADX计算周期 (默认23)
            ADX_M: ADX平滑周期 (默认11)
            VAE_基础TR: VAE基础止盈倍数 (默认1.8)
            VAE_初始止损: VAE初始止损倍数 (默认1.5)
            VAE_周期: VAE计算周期 (默认20)
            固定止损: 固定止损百分比 (默认0.5)
        """
        # SKDJ参数
        self.SKDJ_N = SKDJ_N
        self.SKDJ_M = SKDJ_M

        # CMF参数
        self.CMF_N = CMF_N
        self.CMF_M = CMF_M

        # BIAS参数
        self.BIAS_N = BIAS_N
        self.BIAS_M = BIAS_M

        # ADX参数
        self.ADX_N = ADX_N
        self.ADX_M = ADX_M

        # VAE动态风控参数
        self.VAE_基础TR = VAE_基础TR
        self.VAE_初始止损 = VAE_初始止损
        self.VAE_周期 = VAE_周期
        self.固定止损 = 固定止损

        # 计算所需的最小数据量
        self.min_data_length = max(CMF_N, BIAS_N, ADX_N, VAE_周期 * 2) + 10

    def calculate_SKDJ(self, highs, lows, closes):
        """
        计算SKDJ超卖指标

        参数:
            highs: 最高价数组
            lows: 最低价数组
            closes: 收盘价数组

        返回:
            tuple: (K值数组, D值数组)
        """
        try:
            if len(closes) < self.SKDJ_N + self.SKDJ_M:
                return np.full(len(closes), 50.0), np.full(len(closes), 50.0)

            # LOWV = LLV(LOW, SKDJ_N)
            LOWV = talib.MIN(lows, timeperiod=self.SKDJ_N)
            # HIGHV = HHV(HIGH, SKDJ_N)
            HIGHV = talib.MAX(highs, timeperiod=self.SKDJ_N)

            # RSV = EMA((CLOSE-LOWV)/(HIGHV-LOWV)*100, SKDJ_M)
            RSV_raw = (closes - LOWV) / (HIGHV - LOWV) * 100
            RSV_raw = np.nan_to_num(RSV_raw, nan=50.0)  # 处理除零情况
            RSV = talib.EMA(RSV_raw, timeperiod=self.SKDJ_M)

            # K = EMA(RSV, SKDJ_M)
            K = talib.EMA(RSV, timeperiod=self.SKDJ_M)
            # D = MA(K, SKDJ_M)
            D = talib.SMA(K, timeperiod=self.SKDJ_M)

            return K, D

        except Exception as e:
            print(f"❌ SKDJ计算失败: {e}")
            return np.full(len(closes), 50.0), np.full(len(closes), 50.0)

    def calculate_CMF(self, highs, lows, closes, volumes):
        """
        计算CMF资金流指标

        参数:
            highs: 最高价数组
            lows: 最低价数组
            closes: 收盘价数组
            volumes: 成交量数组

        返回:
            np.ndarray: CMF值数组
        """
        try:
            if len(closes) < self.CMF_N:
                return np.zeros(len(closes))

            # CLV = (CLOSE-LOW-HIGH+CLOSE)/(HIGH-LOW)
            CLV = (closes - lows - highs + closes) / (highs - lows)
            CLV = np.nan_to_num(CLV, nan=0.0)  # 处理除零情况

            # MF = CLV * VOL
            MF = CLV * volumes

            # CMF = SUM(MF, CMF_N) / SUM(VOL, CMF_N)
            MF_sum = talib.SUM(MF, timeperiod=self.CMF_N)
            VOL_sum = talib.SUM(volumes, timeperiod=self.CMF_N)
            CMF = MF_sum / VOL_sum
            CMF = np.nan_to_num(CMF, nan=0.0)

            return CMF

        except Exception as e:
            print(f"❌ CMF计算失败: {e}")
            return np.zeros(len(closes))

    def detect_CMF_divergence(self, highs, lows, CMF):
        """
        检测CMF背离信号

        参数:
            highs: 最高价数组
            lows: 最低价数组
            CMF: CMF指标数组

        返回:
            tuple: (CMF底背离, CMF顶背离)
        """
        try:
            if len(CMF) < self.CMF_M:
                return False, False

            # CMF_HH = HIGH >= HHV(HIGH, CMF_M)
            HHV_high = talib.MAX(highs, timeperiod=self.CMF_M)
            CMF_HH = highs[-1] >= HHV_high[-1]

            # CMF_LL = LOW <= LLV(LOW, CMF_M)
            LLV_low = talib.MIN(lows, timeperiod=self.CMF_M)
            CMF_LL = lows[-1] <= LLV_low[-1]

            # CMF_指标HH = CMF >= HHV(CMF, CMF_M)
            HHV_CMF = talib.MAX(CMF, timeperiod=self.CMF_M)
            CMF_指标HH = CMF[-1] >= HHV_CMF[-1]

            # CMF_指标LL = CMF <= LLV(CMF, CMF_M)
            LLV_CMF = talib.MIN(CMF, timeperiod=self.CMF_M)
            CMF_指标LL = CMF[-1] <= LLV_CMF[-1]

            # CMF顶背离 = CMF_HH AND CMF_指标HH==0 AND CMF>0
            CMF顶背离 = CMF_HH and not CMF_指标HH and CMF[-1] > 0

            # CMF底背离 = CMF_LL AND CMF_指标LL==0 AND CMF<0
            CMF底背离 = CMF_LL and not CMF_指标LL and CMF[-1] < 0

            return CMF底背离, CMF顶背离
        except Exception as e:
            print(f"⚠️ CMF背离检测失败: {e}")
            return False, False

    def calculate_BIAS(self, closes):
        """
        计算BIAS乖离率指标

        参数:
            closes: 收盘价数组

        返回:
            np.array: BIAS指标数组
        """
        try:
            if len(closes) < self.BIAS_N:
                # 数据不足时返回零数组
                return np.zeros(len(closes))

            # BIAS = (CLOSE - MA(CLOSE, BIAS_N)) / MA(CLOSE, BIAS_N) * 100
            ma_close = talib.SMA(closes, timeperiod=self.BIAS_N)

            # 避免除零错误
            ma_close = np.where(ma_close == 0, 1e-8, ma_close)

            BIAS = (closes - ma_close) / ma_close * 100

            # 处理NaN值
            BIAS = np.nan_to_num(BIAS, nan=0.0, posinf=0.0, neginf=0.0)

            return BIAS

        except Exception as e:
            print(f"❌ BIAS计算失败: {e}")
            return np.zeros(len(closes))

    def detect_BIAS_divergence(self, highs, lows, BIAS):
        """
        检测BIAS背离信号

        参数:
            highs: 最高价数组
            lows: 最低价数组
            BIAS: BIAS指标数组

        返回:
            tuple: (BIAS底背离, BIAS顶背离)
        """
        try:
            if len(BIAS) < self.BIAS_M:
                return False, False

            # BIAS_HH = HIGH >= HHV(HIGH, BIAS_M)
            HHV_high = talib.MAX(highs, timeperiod=self.BIAS_M)
            BIAS_HH = highs[-1] >= HHV_high[-1]

            # BIAS_LL = LOW <= LLV(LOW, BIAS_M)
            LLV_low = talib.MIN(lows, timeperiod=self.BIAS_M)
            BIAS_LL = lows[-1] <= LLV_low[-1]

            # BIAS_指标HH = BIAS >= HHV(BIAS, BIAS_M)
            HHV_BIAS = talib.MAX(BIAS, timeperiod=self.BIAS_M)
            BIAS_指标HH = BIAS[-1] >= HHV_BIAS[-1]

            # BIAS_指标LL = BIAS <= LLV(BIAS, BIAS_M)
            LLV_BIAS = talib.MIN(BIAS, timeperiod=self.BIAS_M)
            BIAS_指标LL = BIAS[-1] <= LLV_BIAS[-1]

            # BIAS顶背离 = BIAS_HH AND BIAS_指标HH==0 AND BIAS>0
            BIAS顶背离 = BIAS_HH and not BIAS_指标HH and BIAS[-1] > 0

            # BIAS底背离 = BIAS_LL AND BIAS_指标LL==0 AND BIAS<0
            BIAS底背离 = BIAS_LL and not BIAS_指标LL and BIAS[-1] < 0

            return BIAS底背离, BIAS顶背离
        except Exception as e:
            print(f"⚠️ BIAS背离检测失败: {e}")
            return False, False

    def calculate_ADX(self, highs, lows, closes):
        """
        计算ADX趋势强度指标（严格按照通达信公式）

        通达信公式：
        MTR:=SUM(MAX(MAX(HIGH-LOW,ABS(HIGH-REF(CLOSE,1))),ABS(REF(CLOSE,1)-LOW)),ADX_N);
        HD:=HIGH-REF(HIGH,1);
        LD:=REF(LOW,1)-LOW;
        DMP:=SUM(IF(HD>0&&HD>LD,HD,0),ADX_N);
        DMM:=SUM(IF(LD>0&&LD>HD,LD,0),ADX_N);
        PDI:=DMP*100/MTR;
        MDI:=DMM*100/MTR;
        ADX:=MA(ABS(MDI-PDI)/(MDI+PDI)*100,ADX_M);

        参数:
            highs: 最高价数组
            lows: 最低价数组
            closes: 收盘价数组

        返回:
            np.array: ADX指标数组
        """
        try:
            # 确保输入数据是numpy数组
            highs = np.asarray(highs, dtype=np.float64)
            lows = np.asarray(lows, dtype=np.float64)
            closes = np.asarray(closes, dtype=np.float64)

            if len(closes) < self.ADX_N + 1:
                # 数据不足时返回零数组（只需要ADX_N+1个数据即可开始计算）
                return np.zeros(len(closes))

            # 计算True Range的各个组成部分（避免数组索引错误的安全方法）
            data_len = len(closes)

            # 1. HL = HIGH - LOW
            hl = highs - lows

            # 2. 手动构建HC和LC数组以避免索引错误
            hc = np.zeros(data_len, dtype=np.float64)
            lc = np.zeros(data_len, dtype=np.float64)

            # 第一个值设为HL[0]，其余计算REF差值
            hc[0] = hl[0]
            lc[0] = hl[0]

            for i in range(1, data_len):
                hc[i] = abs(highs[i] - closes[i-1])  # ABS(HIGH - REF(CLOSE,1))
                lc[i] = abs(closes[i-1] - lows[i])   # ABS(REF(CLOSE,1) - LOW)

            # 3. TR = MAX(MAX(HL, HC), LC)
            tr = np.maximum(np.maximum(hl, hc), lc)

            # MTR = SUM(TR, ADX_N)
            MTR = talib.SUM(tr.astype(np.float64), timeperiod=self.ADX_N)

            # 4. 手动构建HD和LD数组以避免索引错误
            HD = np.zeros(data_len, dtype=np.float64)
            LD = np.zeros(data_len, dtype=np.float64)

            # 第一个值设为0，其余计算REF差值
            for i in range(1, data_len):
                HD[i] = highs[i] - highs[i-1]        # HIGH - REF(HIGH,1)
                LD[i] = lows[i-1] - lows[i]          # REF(LOW,1) - LOW

            # 5. 手动构建DMP和DMM值数组以避免条件判断错误
            dmp_values = np.zeros(data_len, dtype=np.float64)
            dmm_values = np.zeros(data_len, dtype=np.float64)

            for i in range(data_len):
                # DMP = IF(HD>0&&HD>LD,HD,0)
                if HD[i] > 0 and HD[i] > LD[i]:
                    dmp_values[i] = HD[i]
                # DMM = IF(LD>0&&LD>HD,LD,0)
                if LD[i] > 0 and LD[i] > HD[i]:
                    dmm_values[i] = LD[i]

            # 6. DMP = SUM(dmp_values, ADX_N), DMM = SUM(dmm_values, ADX_N)
            DMP = talib.SUM(dmp_values, timeperiod=self.ADX_N)
            DMM = talib.SUM(dmm_values, timeperiod=self.ADX_N)

            # 避免除零错误
            MTR = np.where(MTR == 0, 1e-8, MTR)

            # PDI = DMP * 100 / MTR
            PDI = (DMP * 100.0 / MTR).astype(np.float64)

            # MDI = DMM * 100 / MTR
            MDI = (DMM * 100.0 / MTR).astype(np.float64)

            # ADX = MA(ABS(MDI-PDI)/(MDI+PDI)*100, ADX_M)
            dx_numerator = np.abs(MDI - PDI)
            dx_denominator = MDI + PDI
            dx_denominator = np.where(dx_denominator == 0, 1e-8, dx_denominator)
            DX = (dx_numerator / dx_denominator * 100.0).astype(np.float64)

            # 计算ADX（DX的移动平均）
            ADX = talib.SMA(DX, timeperiod=self.ADX_M)

            # 处理NaN值
            ADX = np.nan_to_num(ADX, nan=0.0, posinf=0.0, neginf=0.0)

            return ADX

        except Exception as e:
            print(f"❌ ADX计算失败: {e}")
            return np.zeros(len(closes))

    def calculate_VAE_dynamic_control(self, highs, lows, closes):
        """
        计算VAE动态风控 - 基于实际市场波动的动态止盈止损

        参数:
            highs: 最高价数组
            lows: 最低价数组
            closes: 收盘价数组

        返回:
            dict: VAE动态风控信息，包含基于市场波动的动态止盈止损比例
        """
        try:
            # 优化ATR计算：减少平滑，提高敏感度
            # 短期ATR = ATR(较短周期) - 更敏感地反映近期波动
            短期ATR周期 = max(10, self.VAE_周期 // 2)  # 使用VAE周期的一半，最少10期
            当前ATR = talib.ATR(highs, lows, closes, timeperiod=短期ATR周期)

            # 长期ATR均值 = MA(当前ATR, 适中周期) - 减少过度平滑
            长期ATR周期 = max(20, self.VAE_周期 + 3)  # 比VAE周期稍长，最少20期
            ATR均值 = talib.SMA(当前ATR, timeperiod=长期ATR周期)

            if len(当前ATR) == 0 or len(ATR均值) == 0 or len(closes) == 0:
                return {
                    '动态TR': self.VAE_基础TR,
                    '波动率比值': 1.0,
                    '波动率区间': '正常波动区',
                    '实际波动幅度': 1.5,
                    '动态止盈比例': 1.5,
                    '动态止损比例': 1.0
                }

            # 波动率比值 = 当前ATR / ATR均值
            波动率比值 = 当前ATR[-1] / ATR均值[-1] if ATR均值[-1] > 0 else 1.0

            # 计算实际波动幅度 = ATR / 当前价格 × 100%
            当前价格 = closes[-1]
            实际波动幅度 = (当前ATR[-1] / 当前价格 * 100) if 当前价格 > 0 and 当前ATR[-1] > 0 else 1.5

            # 波动率分区判断和动态止盈止损系数设置
            # 修复：高波动市场应该有更高的止盈/止损比例
            if 波动率比值 <= 0.8:
                波动率区间 = '低波动区'
                动态TR = self.VAE_基础TR * 2  # 保留原有逻辑作为备用
                止盈系数 = 1.2  # 低波动时适度放大止盈
                止损系数 = 0.6  # 相对保守的止损
                基础止盈 = 0.8  # 基础最小止盈比例
                基础止损 = 0.5  # 基础最小止损比例
            elif 波动率比值 > 0.8 and 波动率比值 <= 1.2:
                波动率区间 = '正常波动区'
                动态TR = self.VAE_基础TR
                止盈系数 = 1.0  # 正常波动使用标准系数
                止损系数 = 0.5
                基础止盈 = 1.0
                基础止损 = 0.6
            elif 波动率比值 > 1.2 and 波动率比值 <= 1.8:
                波动率区间 = '高波动区'
                动态TR = self.VAE_基础TR * 1
                止盈系数 = 1.3  # 高波动时要求更高止盈
                止损系数 = 0.5  # 保持相对稳定的止损
                基础止盈 = 1.2
                基础止损 = 0.7
            else:  # 波动率比值 > 1.8
                波动率区间 = '极高波动区'
                动态TR = self.VAE_基础TR * 0.7
                止盈系数 = 1.5  # 极高波动时要求最高止盈
                止损系数 = 0.5  # 保持稳定止损
                基础止盈 = 1.5
                基础止损 = 0.8

            # 计算基于实际市场波动的动态止盈止损比例
            计算止盈比例 = 实际波动幅度 * 止盈系数
            计算止损比例 = 实际波动幅度 * 止损系数

            # 应用基础保护：取计算值和基础值的最大值
            动态止盈比例 = max(计算止盈比例, 基础止盈)
            动态止损比例 = max(计算止损比例, 基础止损)

            # 合理性检查：确保止盈止损比例在合理范围内
            动态止盈比例 = max(0.8, min(动态止盈比例, 8.0))  # 提高最小值到0.8%
            动态止损比例 = max(0.5, min(动态止损比例, 5.0))  # 提高最小值到0.5%

            # 确保止盈比例大于止损比例，且比例合理
            if 动态止盈比例 <= 动态止损比例:
                动态止盈比例 = 动态止损比例 * 1.8  # 提高倍数到1.8

            # 确保最小止盈/止损比例
            min_ratio = 1.5  # 最小止盈/止损比例
            if 动态止盈比例 / 动态止损比例 < min_ratio:
                动态止盈比例 = 动态止损比例 * min_ratio

            return {
                '动态TR': 动态TR,  # 保留原有字段，向后兼容
                '波动率比值': 波动率比值,
                '波动率区间': 波动率区间,
                '当前ATR': 当前ATR[-1],
                'ATR均值': ATR均值[-1],
                # 新增字段：基于市场波动的动态止盈止损
                '实际波动幅度': 实际波动幅度,
                '动态止盈比例': 动态止盈比例,
                '动态止损比例': 动态止损比例,
                '止盈系数': 止盈系数,
                '止损系数': 止损系数,
                # 新增调试信息
                '计算止盈比例': 计算止盈比例,
                '计算止损比例': 计算止损比例,
                '基础止盈': 基础止盈,
                '基础止损': 基础止损,
                '止盈止损比例': 动态止盈比例 / 动态止损比例 if 动态止损比例 > 0 else 0,
                # ATR计算参数信息
                '短期ATR周期': 短期ATR周期,
                '长期ATR周期': 长期ATR周期,
                'ATR优化': '已启用减少平滑'
            }
        except Exception as e:
            print(f"⚠️ VAE动态风控计算失败: {e}")
            return {
                '动态TR': self.VAE_基础TR,
                '波动率比值': 1.0,
                '波动率区间': '正常波动区',
                '实际波动幅度': 1.5,
                '动态止盈比例': 1.5,
                '动态止损比例': 1.0
            }

    def get_comprehensive_signals(self, merged_klines):
        """
        获取综合交易信号

        参数:
            merged_klines: 合成K线数据

        返回:
            dict: 综合交易信号结果
        """
        try:
            # 动态数据需求检查
            current_data_count = len(merged_klines) if merged_klines else 0

            # 最小可用数据检查（至少需要10根K线进行基本计算）
            if current_data_count < 10:
                return {
                    'status': 'insufficient_data',
                    'message': f'数据严重不足：需要至少10根K线进行基本计算，当前只有{current_data_count}根',
                    'buy_signal': False,
                    'sell_signal': False
                }

            # 标准模式数据检查
            if current_data_count < self.min_data_length:
                # 数据不足但可以尝试动态模式
                print(f"⚠️ 数据不足警告: 标准模式需要{self.min_data_length}根K线，当前{current_data_count}根")
                print(f"🔄 尝试启用动态模式进行计算...")

                # 动态调整参数以适应当前数据量
                dynamic_cmf_n = min(self.CMF_N, max(5, current_data_count // 3))
                dynamic_bias_n = min(self.BIAS_N, max(5, current_data_count // 3))
                dynamic_adx_n = min(self.ADX_N, max(5, current_data_count // 3))
                dynamic_vae_period = min(self.VAE_周期, max(5, current_data_count // 4))

                print(f"   📊 动态参数: CMF={dynamic_cmf_n}, BIAS={dynamic_bias_n}, ADX={dynamic_adx_n}, VAE={dynamic_vae_period}")

                # 临时更新参数用于本次计算
                original_params = {
                    'CMF_N': self.CMF_N, 'BIAS_N': self.BIAS_N,
                    'ADX_N': self.ADX_N, 'VAE_周期': self.VAE_周期
                }
                self.CMF_N = dynamic_cmf_n
                self.BIAS_N = dynamic_bias_n
                self.ADX_N = dynamic_adx_n
                self.VAE_周期 = dynamic_vae_period

            # 提取OHLCV数据
            opens = np.array([k['open'] for k in merged_klines], dtype=np.float64)
            highs = np.array([k['high'] for k in merged_klines], dtype=np.float64)
            lows = np.array([k['low'] for k in merged_klines], dtype=np.float64)
            closes = np.array([k['close'] for k in merged_klines], dtype=np.float64)
            volumes = np.array([k['volume'] for k in merged_klines], dtype=np.float64)

            # 计算各项指标
            K, D = self.calculate_SKDJ(highs, lows, closes)
            CMF = self.calculate_CMF(highs, lows, closes, volumes)
            BIAS = self.calculate_BIAS(closes)
            ADX = self.calculate_ADX(highs, lows, closes)
            VAE_info = self.calculate_VAE_dynamic_control(highs, lows, closes)

            # 检测背离信号
            CMF底背离, CMF顶背离 = self.detect_CMF_divergence(highs, lows, CMF)
            BIAS底背离, BIAS顶背离 = self.detect_BIAS_divergence(highs, lows, BIAS)

            # 计算阻力线突破（修正版：当前收盘价突破前一根K线的阻力线）
            if len(closes) < 2:
                突破条件 = False  # 数据不足，无法判断突破
            else:
                # 使用前一根K线计算阻力线
                前一根K线加权均值 = (highs[-2] + lows[-2] + 2 * closes[-2]) / 4
                前一根阻力线 = 前一根K线加权均值 + (前一根K线加权均值 - lows[-2])
                突破条件 = closes[-1] > 前一根阻力线  # 当前收盘价突破前一根阻力线

            # 买入条件组合
            SKDJ超卖 = K[-1] < 20 and D[-1] < 20

            # 双重背离时间窗口容错机制（与通达信公式一致）
            # 允许前1-2根K线的背离信号，避免错过机会
            CMF背离窗口 = CMF底背离  # 当前K线
            BIAS背离窗口 = BIAS底背离  # 当前K线

            # 检查前1根K线的背离信号（如果数据足够）
            if len(highs) > 1:
                CMF底背离_1, _ = self.detect_CMF_divergence(highs[:-1], lows[:-1], CMF[:-1])
                BIAS底背离_1, _ = self.detect_BIAS_divergence(highs[:-1], lows[:-1], BIAS[:-1])
                CMF背离窗口 = CMF背离窗口 or CMF底背离_1
                BIAS背离窗口 = BIAS背离窗口 or BIAS底背离_1

            # 检查前2根K线的背离信号（如果数据足够）
            if len(highs) > 2:
                CMF底背离_2, _ = self.detect_CMF_divergence(highs[:-2], lows[:-2], CMF[:-2])
                BIAS底背离_2, _ = self.detect_BIAS_divergence(highs[:-2], lows[:-2], BIAS[:-2])
                CMF背离窗口 = CMF背离窗口 or CMF底背离_2
                BIAS背离窗口 = BIAS背离窗口 or BIAS底背离_2

            # 双重背离确认：CMF和BIAS都在时间窗口内出现底背离
            双重背离 = CMF背离窗口 and BIAS背离窗口

            强趋势确认 = ADX[-1] > 40
            突破确认 = 突破条件

            # 最终买入信号
            买入信号 = SKDJ超卖 and 双重背离 and 强趋势确认 and 突破确认

            result = {
                'status': 'success',
                'buy_signal': 买入信号,
                'sell_signal': False,
                'indicators': {
                    'SKDJ_K': K[-1],
                    'SKDJ_D': D[-1],
                    'CMF': CMF[-1],
                    'BIAS': BIAS[-1],
                    'ADX': ADX[-1],
                    'resistance_line': 前一根阻力线 if len(closes) >= 2 else 0,
                    'current_price': closes[-1]
                },
                'conditions': {
                    'SKDJ超卖': SKDJ超卖,
                    'CMF底背离': CMF底背离,
                    'BIAS底背离': BIAS底背离,
                    '双重背离': 双重背离,
                    '强趋势确认': 强趋势确认,
                    '突破确认': 突破确认
                },
                'VAE_info': VAE_info,
                'data_quality': {
                    'total_bars': len(merged_klines),
                    'min_required': self.min_data_length,
                    'data_coverage': len(merged_klines) / self.min_data_length,
                    'dynamic_mode': current_data_count < self.min_data_length
                }
            }

            # 恢复原始参数（如果使用了动态模式）
            if 'original_params' in locals():
                self.CMF_N = original_params['CMF_N']
                self.BIAS_N = original_params['BIAS_N']
                self.ADX_N = original_params['ADX_N']
                self.VAE_周期 = original_params['VAE_周期']
                print(f"🔄 已恢复原始参数")

            return result

        except Exception as e:
            # 恢复原始参数（如果使用了动态模式）
            if 'original_params' in locals():
                self.CMF_N = original_params['CMF_N']
                self.BIAS_N = original_params['BIAS_N']
                self.ADX_N = original_params['ADX_N']
                self.VAE_周期 = original_params['VAE_周期']
                print(f"🔄 异常处理中已恢复原始参数")

            return {
                'status': 'calculation_error',
                'error_message': str(e),
                'buy_signal': False,
                'sell_signal': False
            }

def init(C):
    """
    策略初始化函数 - QMT标准框架

    参数:
        C: QMT策略上下文对象
    """
    try:
        print("="*80)
        print("🚀 CMF+BIAS双重背离策略实盘版初始化开始")
        print("="*80)

        # 获取实际交易账户信息（从模型交易界面配置）- 使用QMT官方标准方法
        # 注意：account 和 accountType 是QMT在策略运行时自动传递的全局变量
        try:
            C.acct = account          # 真实交易账户ID（QMT自动传递）
            C.acct_type = accountType # 账户类型：STOCK(普通) 或 CREDIT(信用)
            print(f"📊 账户信息: ID={C.acct}, 类型={C.acct_type}")
        except NameError:
            # 如果在非QMT环境中运行，使用备用方案
            print("⚠️ 未检测到QMT账户信息，请在QMT环境中运行")
            C.acct = "请在QMT环境中运行"
            C.acct_type = "STOCK"

        # ============================================================================
        # CMF+BIAS双重背离策略核心参数
        # ============================================================================

        # SKDJ超卖指标参数
        C.SKDJ_N = 8                   # SKDJ计算周期
        C.SKDJ_M = 4                   # SKDJ平滑周期

        # CMF资金流背离参数 - 适度优化等待时间
        C.CMF_N = 25                   # CMF计算周期 (30→25，减少17%)
        C.CMF_M = 18                   # CMF背离判断周期 (20→18，保持比例)

        # BIAS乖离率背离参数 - 适度优化等待时间
        C.BIAS_N = 25                  # BIAS计算周期 (30→25，减少17%)
        C.BIAS_M = 18                  # BIAS背离判断周期 (20→18，保持比例)

        # ADX趋势强度参数 - 适度优化等待时间
        C.ADX_N = 20                   # ADX计算周期 (23→20，减少13%)
        C.ADX_M = 5                   # ADX平滑周期 (11→5，减少50%)

        # VAE动态风控参数 - 适度优化等待时间
        C.VAE_基础TR = 1.5             # VAE基础止盈倍数
        C.VAE_初始止损 = 1.1           # VAE初始止损倍数
        C.VAE_周期 = 17                # VAE计算周期 (20→17，减少15%)

        # 固定风控参数
        C.固定止损 = 0.5               # 固定止损百分比

        # 风险控制参数
        C.stop_loss_pct = 0.05         # 止损百分比 (5%)
        C.take_profit_pct = 0.10       # 止盈百分比 (10%)


        # 基础设置
        C.stock = C.stockcode + '.' + C.market
        C.previous_volume = 0

        print(f"📊 交易标的: {C.stock}")

        # K线合成相关变量（保持原有逻辑）
        C.kline_buffer = []
        C.merged_klines = []
        C.bar_count = 0
        C.MAX_BUFFER_SIZE = 10

        # 背离检测器实例
        C.divergence_detector = None

        # 兼容性参数（用于滑动窗口维护）
        C.ATRLength = max(C.CMF_N, C.BIAS_N, C.ADX_N)  # 基于CMF+BIAS策略的最大周期
        C.VolLen = C.VAE_周期  # 基于VAE周期

        # 动态数据积累相关变量
        C.use_dynamic_mode = False  # 动态模式标志
        C.dynamic_data_count = 0    # 动态数据计数器
        C.max_history_bars = max(C.CMF_N, C.BIAS_N, C.ADX_N, C.VAE_周期 * 2) + 10  # 保存足够的历史数据
        
        # 实盘风险控制参数
        C.max_position_ratio = 1  # 最大仓位比例 (100%)
        C.min_cash_reserve = 0  # 最小资金保留
        C.max_single_loss = 0.1   # 单笔最大亏损比例 (2%)

        # 挂单价格偏移量参数 - 确保成交策略
        C.buy_hang_offset_ratio = 0.002   # 买入挂单向上偏移比例 (0.2%) - 加价买入
        C.sell_hang_offset_ratio = 0.002  # 卖出挂单向下偏移比例 (0.2%) - 降价卖出
        
        # 策略状态变量
        C.position = 0
        C.entry_price = 0
        C.exit_price = 0
        C.bars_since_entry = 0
        C.last_trade_time = None

        # 移动止盈线相关变量
        C.highest_price_since_entry = 0      # 入场后最高价格
        C.trailing_stop_price = 0            # 移动止盈线价格
        C.use_trailing_stop = True           # 是否启用移动止盈线
        C.trailing_stop_ratio = 0.02         # 移动止盈回撤比例 (2%)

        # 委托管理：防止重复下单的关键机制
        C.waiting_list = []          # 未确认委托列表
        C.last_order_time = None     # 最后下单时间
        C.order_cooldown = 5         # 下单冷却时间（秒）
        C.max_pending_orders = 2     # 最大未确认委托数量

        # 撤单和订单管理设置
        C.auto_cancel_orders = True    # 是否自动撤销未成交订单
        C.max_order_age = 300          # 订单最大存活时间（秒）
        C.cancel_before_new_order = True  # 新订单前是否撤销旧订单
        C.enable_order_monitoring = True  # 是否启用订单状态监控

        # 实盘监控变量
        C.total_trades = 0
        C.successful_trades = 0
        C.failed_trades = 0
        C.last_error = None
        
        print(f"📊 交易标的: {C.stock}")
        print(f"📈 SKDJ参数: N={C.SKDJ_N}, M={C.SKDJ_M}")
        print(f"💰 CMF参数: N={C.CMF_N}, M={C.CMF_M}")
        print(f"📊 BIAS参数: N={C.BIAS_N}, M={C.BIAS_M}")
        print(f"💪 ADX参数: N={C.ADX_N}, M={C.ADX_M}")
        print(f"🌊 VAE参数: 基础TR={C.VAE_基础TR}%, 初始止损={C.VAE_初始止损}%, 周期={C.VAE_周期}")
        print(f"🛡️ 风险控制: 固定止损{C.固定止损}%")
        print(f"🔧 兼容参数: ATRLength={C.ATRLength}, VolLen={C.VolLen}")

        # 尝试初始化历史数据（可选）
        print(f"\n� 尝试初始化历史数据...")
        init_success = try_initialize_historical_data(C)

        if init_success:
            print(f"✅ 历史数据初始化成功 - 策略使用标准模式")
            print(f"   📊 历史数据数量: {len(C.merged_klines)}个K线")
            C.use_dynamic_mode = False  # 使用标准模式
        else:
            print(f"⚠️ 历史数据初始化失败 - 启用动态数据积累模式")
            print(f"   🔄 将使用实时K线合成数据逐步积累，立即开始交易")
            print(f"   📊 保持原有K线合成和增量成交量计算逻辑")
            C.use_dynamic_mode = True   # 启用动态模式
            C.dynamic_data_count = 0    # 动态数据计数器

        # ============================================================================
        # CMF+BIAS双重背离策略配置
        # ============================================================================
        C.enable_divergence_strategy = True    # 背离策略开关

        # 背离检测器实例（延迟初始化）
        C.divergence_detector = None

        print(f"📊 CMF+BIAS双重背离策略配置:")
        print(f"   启用状态: {C.enable_divergence_strategy}")
        print(f"   策略类型: CMF+BIAS双重背离 + SKDJ超卖 + ADX强趋势 + VAE动态风控")

        # 输出挂单参数说明
        print_hang_order_config(C)

        print("✅ 策略初始化完成")
        print("="*80)

    except Exception as e:
        print(f"❌ 策略初始化失败: {e}")
        traceback.print_exc()

def print_hang_order_config(C):
    """
    输出挂单配置参数说明
    """
    print(f"\n📋 挂单模式配置 - 确保成交策略:")
    print(f"   � 买入挂单: 向上偏移 {C.buy_hang_offset_ratio*100:.2f}% (加价买入)")
    print(f"   � 卖出挂单: 向下偏移 {C.sell_hang_offset_ratio*100:.2f}% (降价卖出)")
    print(f"   💡 说明: 买入价格 = 当前价格 × (1 + 偏移比例), 卖出价格 = 当前价格 × (1 - 偏移比例)")
    print(f"   🎯 策略: 优先确保成交，愿意付出价格代价")
    print(f"   ⚙️ 调整方法: 修改 C.buy_hang_offset_ratio 和 C.sell_hang_offset_ratio 参数")
    print(f"   📊 建议范围: 0.001-0.005 (0.1%-0.5%)")

def set_hang_order_offset(C, buy_offset=None, sell_offset=None):
    """
    动态设置挂单价格偏移量 - 确保成交策略

    参数:
        C: 策略上下文
        buy_offset: 买入挂单向上偏移比例 (如 0.002 表示0.2%加价买入)
        sell_offset: 卖出挂单向下偏移比例 (如 0.002 表示0.2%降价卖出)
    """
    if buy_offset is not None:
        C.buy_hang_offset_ratio = buy_offset
        print(f"✅ 买入挂单偏移量已设置为: 向上{buy_offset*100:.2f}% (加价买入)")

    if sell_offset is not None:
        C.sell_hang_offset_ratio = sell_offset
        print(f"✅ 卖出挂单偏移量已设置为: 向下{sell_offset*100:.2f}% (降价卖出)")

def set_trailing_stop_config(C, enable=None, ratio=None):
    """
    动态设置移动止盈配置

    参数:
        C: 策略上下文
        enable: 是否启用移动止盈 (True/False)
        ratio: 移动止盈回撤比例 (如 0.02 表示2%回撤)
    """
    if enable is not None:
        C.use_trailing_stop = enable
        print(f"✅ 移动止盈已{'启用' if enable else '禁用'}")

    if ratio is not None:
        C.trailing_stop_ratio = ratio
        print(f"✅ 移动止盈回撤比例已设置为: {ratio*100:.1f}%")

    # 显示当前配置
    print(f"📊 当前移动止盈配置:")
    print(f"   状态: {'启用' if C.use_trailing_stop else '禁用'}")
    print(f"   基础回撤比例: {C.trailing_stop_ratio*100:.1f}%")
    print(f"   动态调整规则:")
    print(f"     - 低波动区: {C.trailing_stop_ratio*0.5*100:.1f}% (基础×0.5)")
    print(f"     - 正常波动区: {C.trailing_stop_ratio*100:.1f}% (基础×1.0)")
    print(f"     - 高波动区: {C.trailing_stop_ratio*1.5*100:.1f}% (基础×1.5)")
    print(f"     - 极高波动区: {C.trailing_stop_ratio*2.0*100:.1f}% (基础×2.0)")

def handlebar(C):
    if not C.is_last_bar():
            return

    """
    主策略处理函数 - QMT标准框架
    
    参数:
        C: QMT策略上下文对象
    """
    try:
        # 获取当前时间
        current_time = datetime.datetime.now()
        bar_time = current_time.strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"\n[{bar_time}] 策略执行开始")

        # 获取市场数据
        market_data = get_safe_market_data(C, bar_time)
        if not market_data:
            return
            
        last_price, volume = market_data
        
        # 智能成交量处理 - 自动检测累计/增量模式
        last_volume = process_volume_data(C, volume)
        
        # K线合成处理
        process_kline_merge(C, bar_time, last_price, last_volume, current_time)
        
        # 策略交易逻辑
        execute_trading_strategy(C, bar_time)
        
    except Exception as e:
        print(f"❌ 策略执行错误: {e}")
        C.last_error = str(e)
        traceback.print_exc()

# is_trading_time函数已移除 - 策略现在可以在任何时间执行

def process_volume_data(C, current_volume):
    """
    处理累计成交量数据，转换为增量成交量

    参数:
        C: 策略上下文
        current_volume: 当前获取的累计成交量数据（来自QMT API）

    返回:
        float: 处理后的增量成交量
    """
    try:
        current_volume = float(current_volume)

        # 初始化成交量处理相关变量
        if not hasattr(C, 'volume_processor_initialized'):
            C.volume_anomaly_stats = {'count': 0, 'total_corrections': 0}
            C.volume_processor_initialized = True
            print("📊 累计成交量转增量处理器初始化完成")

        # 计算增量成交量：当前累计成交量 - 上次累计成交量
        if C.previous_volume == 0:
            # 第一次运行，无法计算增量，使用保守估算
            volume_increment = max(1, current_volume / 1000)  # 假设是累计的千分之一作为增量
            print(f"📊 首次运行，累计成交量: {current_volume:.0f}, 估算增量: {volume_increment:.0f}")
        else:
            # 正常计算增量
            raw_increment = current_volume - C.previous_volume

            if raw_increment > 0:
                volume_increment = raw_increment
                print(f"📊 增量成交量: {volume_increment:.0f} (累计: {current_volume:.0f})")
            elif raw_increment == 0:
                # 成交量没有变化，使用最小增量
                volume_increment = 1
                print(f"📊 成交量无变化，使用最小增量: {volume_increment:.0f}")
            else:
                # 负增量，可能是数据重置或异常，使用保守估算
                volume_increment = max(1, current_volume / 1000)
                print(f"⚠️ 检测到负增量: {raw_increment:.0f}, 使用保守估算: {volume_increment:.0f}")

        # 异常值检测和修正（基于历史数据）
        if hasattr(C, 'volume_validation_result') and C.volume_validation_result:
            avg_preload_volume = C.volume_validation_result['volume_stats']['average']
            if avg_preload_volume > 0 and volume_increment > avg_preload_volume * 10:
                print(f"🚨 检测到异常巨大的增量成交量: {volume_increment:.0f} (平均值的{volume_increment/avg_preload_volume:.1f}倍)")
                original_volume = volume_increment
                volume_increment = min(volume_increment, avg_preload_volume * 3)
                print(f"   🔧 修正为保守值: {volume_increment:.0f}")

                C.volume_anomaly_stats['count'] += 1
                C.volume_anomaly_stats['total_corrections'] += abs(original_volume - volume_increment)

                # 每10次异常输出统计
                if C.volume_anomaly_stats['count'] % 10 == 0:
                    print(f"📊 成交量异常统计: 发生{C.volume_anomaly_stats['count']}次，累计修正{C.volume_anomaly_stats['total_corrections']:.0f}")

        # 更新previous_volume为当前累计成交量
        C.previous_volume = current_volume

        return volume_increment

    except Exception as e:
        print(f"❌ 成交量处理失败: {e}")
        return 1  # 返回默认值

def get_safe_market_data(C, bar_time):
    """
    安全获取市场数据
    
    参数:
        C: 策略上下文
        bar_time: 当前时间
        
    返回:
        tuple: (价格, 成交量) 或 None
    """
    try:
        # 使用QMT标准API获取数据
        local_data = C.get_market_data_ex(
            ['lastPrice', 'volume'], 
            [C.stock], 
            period=C.period, 
            count=1, 
            subscribe=False
        )
        
        # 数据有效性检查
        if not local_data or C.stock not in local_data:
            print(f"⚠️ 无法获取{C.stock}的市场数据")
            return None
            
        stock_data = local_data[C.stock]

        # 修复pandas Series布尔值判断错误 - 使用安全的数据检查方法
        def is_data_valid(data):
            """检查数据是否有效（支持pandas Series、numpy数组、列表等）"""
            if data is None:
                return False
            # 检查pandas Series
            if hasattr(data, 'empty'):
                return not data.empty
            # 检查numpy数组或列表
            if hasattr(data, '__len__'):
                return len(data) > 0
            return True

        # 检查lastPrice数据
        last_price_data = stock_data.get('lastPrice')
        if not is_data_valid(last_price_data):
            print(f"⚠️ lastPrice数据无效")
            return None

        # 检查volume数据
        volume_data = stock_data.get('volume')
        if not is_data_valid(volume_data):
            print(f"⚠️ volume数据无效")
            return None

        # 安全提取数据
        try:
            last_price = round(float(last_price_data[0]), 3)
            volume = float(volume_data[0])
        except (IndexError, TypeError, ValueError) as e:
            print(f"⚠️ 数据提取失败: {e}")
            return None
        
        # 价格合理性检查
        if last_price <= 0:
            print(f"⚠️ 异常价格: {last_price}")
            return None
            
        print(f"📊 市场数据: 价格={last_price}, 成交量={volume}")
        return (last_price, volume)
        
    except Exception as e:
        print(f"❌ 数据获取失败: {e}")
        return None

def process_kline_merge(C, bar_time, last_price, last_volume, dt_obj):
    """
    处理K线合成逻辑
    
    参数:
        C: 策略上下文
        bar_time: 时间字符串
        last_price: 当前价格
        last_volume: 当前成交量
        dt_obj: 时间对象
    """
    # 构建当前数据
    current_data = {
        'time': bar_time,
        'price': last_price,
        'volume': last_volume,
        'datetime': dt_obj
    }
    
    C.kline_buffer.append(current_data)
    C.bar_count += 1
    
    print(f"📈 K线缓冲区: {len(C.kline_buffer)}个数据点")
    
    # 2根K线合成1根K线的非重叠合成模式
    if len(C.kline_buffer) >= 2:
        merge_count = 0
        while len(C.kline_buffer) >= 2:
            merged_kline = merge_two_bars(C.kline_buffer[:2])
            C.merged_klines.append(merged_kline)

            print(f"✅ 合成K线{merge_count + 1}: OHLC[{merged_kline['open']}, {merged_kline['high']}, {merged_kline['low']}, {merged_kline['close']}]")

            C.kline_buffer = C.kline_buffer[2:]
            merge_count += 1

            # 动态模式计数器更新
            if hasattr(C, 'use_dynamic_mode') and C.use_dynamic_mode:
                C.dynamic_data_count += 1

        print(f"🔄 本次合成{merge_count}个K线，剩余缓冲区: {len(C.kline_buffer)}")

        # 动态模式状态显示
        if hasattr(C, 'use_dynamic_mode') and C.use_dynamic_mode and merge_count > 0:
            required_bars = max(C.ATRLength, C.VolLen)
            current_bars = len(C.merged_klines)
            quality_level = get_data_quality_level(current_bars, required_bars)
            print(f"🔄 动态积累: {current_bars}个合成K线, {quality_level}")
    
    # 缓冲区异常清理
    if len(C.kline_buffer) > 5:
        C.kline_buffer = C.kline_buffer[-1:]
        print("⚠️ 缓冲区异常增长，已清理")

    # 滑动窗口数据管理 - 关键修正
    maintain_sliding_window(C)

def maintain_sliding_window(C):
    """
    维护滑动窗口数据管理 - 实现FIFO机制

    参数:
        C: 策略上下文
    """
    try:
        # 计算最大K线数量：确保满足策略计算需求
        # 使用max_history_bars确保与背离检测器的min_data_length一致
        MAX_KLINES = C.max_history_bars + 20  # 额外缓冲20根K线，确保数据充足

        current_count = len(C.merged_klines)

        if current_count > MAX_KLINES:
            # FIFO：移除最旧的数据，保留最新的数据
            removed_count = current_count - MAX_KLINES
            C.merged_klines = C.merged_klines[-MAX_KLINES:]

            print(f"🔄 滑动窗口清理: 移除{removed_count}个旧K线，保留{len(C.merged_klines)}个")

            # 更新滑动窗口统计
            if not hasattr(C, 'sliding_window_stats'):
                C.sliding_window_stats = {'total_removed': 0, 'cleanup_count': 0}

            C.sliding_window_stats['total_removed'] += removed_count
            C.sliding_window_stats['cleanup_count'] += 1

            # 每10次清理输出一次统计
            if C.sliding_window_stats['cleanup_count'] % 10 == 0:
                print(f"📊 滑动窗口统计: 已清理{C.sliding_window_stats['cleanup_count']}次，"
                      f"累计移除{C.sliding_window_stats['total_removed']}个K线")

        # 内存优化：定期强制垃圾回收
        if hasattr(C, 'sliding_window_stats') and C.sliding_window_stats['cleanup_count'] % 50 == 0:
            import gc
            gc.collect()
            print("🧹 执行内存垃圾回收")

    except Exception as e:
        print(f"❌ 滑动窗口维护失败: {e}")

def execute_trading_strategy(C, bar_time):
    """
    执行交易策略逻辑
    
    参数:
        C: 策略上下文
        bar_time: 当前时间
    """
    # 检查数据充足性 - 支持动态模式
    min_required_klines = max(C.ATRLength, C.VolLen)
    current_klines_count = len(C.merged_klines)

    # 标准模式：需要足够的历史数据
    if not hasattr(C, 'use_dynamic_mode') or not C.use_dynamic_mode:
        if current_klines_count < min_required_klines:
            print(f"📊 标准模式数据不足: {current_klines_count}/{min_required_klines}, 继续等待")
            return
    else:
        # 动态模式：至少需要2根K线才能开始交易
        if current_klines_count < 2:
            print(f"🔄 动态模式: 数据积累中 {current_klines_count}/2, 继续等待")
            return
        else:
            # 显示动态模式状态
            quality_level = get_data_quality_level(current_klines_count, min_required_klines)
            print(f"� 动态模式: {current_klines_count}个K线, 质量等级: {quality_level}")
    
    # 计算技术指标
    indicators = calculate_technical_indicators(C)
    if not indicators:
        return
    
    # 获取账户信息
    account_info = get_safe_account_info(C)
    if not account_info:
        return
    
    available_cash, current_position = account_info
    
    # 同步策略状态
    sync_position_state(C, current_position)
    
    # 输出当前状态
    print_strategy_status(C, indicators, available_cash, current_position, bar_time)
    
    # 执行交易决策
    if current_position == 0:
        # 无持仓，检查开仓条件
        check_entry_conditions(C, indicators, available_cash, bar_time)
    else:
        # 有持仓，检查平仓条件
        check_exit_conditions(C, indicators, current_position, bar_time)

def calculate_technical_indicators(C):
    """
    计算CMF+BIAS双重背离策略技术指标

    参数:
        C: 策略上下文

    返回:
        dict: 技术指标字典 或 None
    """
    try:
        # 使用合成K线数据进行计算
        calc_klines = C.merged_klines
        data_count = len(calc_klines)

        # 检查数据充足性 - 动态模式支持
        min_required_bars = max(C.CMF_N, C.BIAS_N, C.ADX_N, C.VAE_周期 * 2) + 10

        # 动态模式：允许使用较少的数据进行计算
        if hasattr(C, 'use_dynamic_mode') and C.use_dynamic_mode:
            # 动态模式最少需要10根K线才能进行基本计算
            if data_count < 10:
                print(f"🔄 动态模式数据不足: 需要至少10根K线，当前{data_count}根")
                return None
            else:
                print(f"🔄 动态模式指标计算: 使用{data_count}根K线 (标准需求{min_required_bars}根)")
        else:
            # 标准模式：需要完整的历史数据
            if data_count < min_required_bars:
                print(f"⚠️ 标准模式数据不足: 需要{min_required_bars}根K线，当前{data_count}根")
                return None

        # 动态调整计算参数
        if hasattr(C, 'use_dynamic_mode') and C.use_dynamic_mode:
            # 动态模式：根据可用数据调整指标周期参数
            dynamic_cmf_n = min(C.CMF_N, data_count - 1) if data_count > 1 else 1
            dynamic_bias_n = min(C.BIAS_N, data_count - 1) if data_count > 1 else 1
            dynamic_adx_n = min(C.ADX_N, data_count - 1) if data_count > 1 else 1
            dynamic_vae_period = min(C.VAE_周期, data_count - 1) if data_count > 1 else 1
            print(f"🔄 动态模式CMF+BIAS计算: {data_count}个K线")
            print(f"   📊 动态周期: CMF={dynamic_cmf_n}, BIAS={dynamic_bias_n}, ADX={dynamic_adx_n}, VAE={dynamic_vae_period}")
        else:
            # 标准模式：使用固定参数和滑动窗口优化
            dynamic_cmf_n = C.CMF_N
            dynamic_bias_n = C.BIAS_N
            dynamic_adx_n = C.ADX_N
            dynamic_vae_period = C.VAE_周期

            MAX_CALC_KLINES = min_required_bars + 10
            if data_count > MAX_CALC_KLINES:
                calc_klines = C.merged_klines[-MAX_CALC_KLINES:]
                data_count = len(calc_klines)
                print(f"📊 标准模式CMF+BIAS计算: 使用最近{data_count}个K线（滑动窗口优化）")
            else:
                print(f"📊 标准模式CMF+BIAS计算: 使用全部{data_count}个K线")

        # 从K线数据提取OHLCV数据
        opens = np.array([kline['open'] for kline in calc_klines], dtype=np.float64)
        highs = np.array([kline['high'] for kline in calc_klines], dtype=np.float64)
        lows = np.array([kline['low'] for kline in calc_klines], dtype=np.float64)
        closes = np.array([kline['close'] for kline in calc_klines], dtype=np.float64)
        volumes = np.array([kline['volume'] for kline in calc_klines], dtype=np.float64)



        # 数据质量标记
        data_quality = "动态模式" if (hasattr(C, 'use_dynamic_mode') and C.use_dynamic_mode) else "标准模式"

        # 基础指标结果
        indicators = {
            'opens': opens,
            'highs': highs,
            'lows': lows,
            'closes': closes,
            'volumes': volumes,

            'data_quality': data_quality,
            'data_count': data_count,
            'current_price': closes[-1],

        }

        # 计算CMF+BIAS双重背离信号
        try:
            # 动态模式下需要重新创建检测器以使用动态参数
            if hasattr(C, 'use_dynamic_mode') and C.use_dynamic_mode:
                # 每次都重新创建检测器，使用当前的动态参数
                C.divergence_detector = CMFBIASDivergenceDetector(
                    SKDJ_N=C.SKDJ_N,
                    SKDJ_M=C.SKDJ_M,
                    CMF_N=dynamic_cmf_n,
                    CMF_M=min(C.CMF_M, dynamic_cmf_n),
                    BIAS_N=dynamic_bias_n,
                    BIAS_M=min(C.BIAS_M, dynamic_bias_n),
                    ADX_N=dynamic_adx_n,
                    ADX_M=min(C.ADX_M, dynamic_adx_n),
                    VAE_基础TR=C.VAE_基础TR,
                    VAE_初始止损=C.VAE_初始止损,
                    VAE_周期=dynamic_vae_period,
                    固定止损=C.固定止损
                )
            elif C.divergence_detector is None:
                # 标准模式：只初始化一次
                C.divergence_detector = CMFBIASDivergenceDetector(
                    SKDJ_N=C.SKDJ_N,
                    SKDJ_M=C.SKDJ_M,
                    CMF_N=C.CMF_N,
                    CMF_M=C.CMF_M,
                    BIAS_N=C.BIAS_N,
                    BIAS_M=C.BIAS_M,
                    ADX_N=C.ADX_N,
                    ADX_M=C.ADX_M,
                    VAE_基础TR=C.VAE_基础TR,
                    VAE_初始止损=C.VAE_初始止损,
                    VAE_周期=C.VAE_周期,
                    固定止损=C.固定止损
                )

            # 获取综合交易信号
            divergence_result = C.divergence_detector.get_comprehensive_signals(calc_klines)

            # 添加到指标结果中
            indicators['divergence'] = divergence_result

            # 输出综合交易信号信息
            if divergence_result['status'] == 'success':
                buy_signal = divergence_result.get('buy_signal', False)
                sell_signal = divergence_result.get('sell_signal', False)
                conditions = divergence_result.get('conditions', {})

                if buy_signal:
                    print(f"🎯 检测到买入信号: 双重背离={conditions.get('双重背离', False)}, 强趋势={conditions.get('强趋势确认', False)}")
                elif sell_signal:
                    print(f"📉 检测到卖出信号")
                else:
                    print(f"📊 无有效交易信号")
            else:
                print(f"⚠️ 综合信号检测状态: {divergence_result['status']}")

        except Exception as e:
            print(f"⚠️ CMF+BIAS双重背离计算失败: {e}")
            indicators['divergence'] = {
                'status': 'calculation_error',
                'error_message': str(e),
                'buy_signal': False,
                'sell_signal': False
            }

        return indicators

    except Exception as e:
        print(f"❌ 技术指标计算失败: {e}")
        return None

def get_safe_account_info(C):
    """
    安全获取账户信息 - 实盘版本

    参数:
        C: 策略上下文

    返回:
        tuple: (可用资金, 当前持仓) 或 None
    """
    try:
        # 获取账户信息 - 使用QMT内置API和官方账户ID
        account_info = get_trade_detail_data(C.acct, 'stock', 'account')
        if not account_info or len(account_info) == 0:
            print("⚠️ 无法获取账户信息，请检查账户ID和连接状态")
            return None

        # 安全提取可用资金
        try:
            available_cash = float(account_info[0].m_dAvailable)
            if available_cash < 0:
                print(f"⚠️ 账户资金异常: {available_cash}")
                return None
        except (AttributeError, ValueError, IndexError) as e:
            print(f"⚠️ 账户资金解析失败: {e}")
            return None

        # 获取持仓信息 - 使用官方账户ID
        holdings = get_trade_detail_data(C.acct, 'stock', 'position')
        current_position = 0

        if holdings:
            for holding in holdings:
                try:
                    stock_key = holding.m_strInstrumentID + '.' + holding.m_strExchangeID
                    if stock_key == C.stock:
                        current_position = int(holding.m_nVolume)
                        print(f"📦 当前持仓: {current_position}股")
                        break
                except AttributeError as e:
                    print(f"⚠️ 持仓信息解析错误: {e}")
                    continue

        print(f"💰 账户状态: 可用资金={available_cash:.2f}, 持仓={current_position}股")
        return (available_cash, current_position)

    except NameError as e:
        print(f"❌ QMT函数未定义: {e}")
        print("⚠️ 请确保在QMT环境中运行此策略")
        return None
    except Exception as e:
        print(f"❌ 账户信息获取失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def sync_position_state(C, current_position):
    """
    同步持仓状态

    参数:
        C: 策略上下文
        current_position: 实际持仓数量
    """
    if current_position > 0:
        if C.position == 0:
            # 发现新持仓，可能是外部交易或上次重启后的状态
            print("🔄 检测到持仓状态变化，同步策略状态")
            C.position = 1
            C.bars_since_entry = 0
        else:
            C.bars_since_entry += 1
    else:
        if C.position == 1:
            # 持仓已清空，重置状态
            print("🔄 持仓已清空，重置策略状态")
            reset_position_state(C)

def print_strategy_status(C, indicators, available_cash, current_position, bar_time):
    """
    输出策略状态信息 - 支持动态模式显示 (CMF+BIAS背离策略版本)
    """
    current_close = indicators['closes'][-1]

    # K线数据状态
    total_klines = len(C.merged_klines)

    print(f"\n📊 [{bar_time}] 策略状态:")
    print(f"   💰 可用资金: {available_cash:.2f}")
    print(f"   📈 当前价格: {current_close:.3f}")
    print(f"   📦 持仓状态: {C.position} (实际: {current_position})")
    print(f"   📈 合成K线: {total_klines}个")

    # 显示CMF+BIAS背离策略相关信息
    divergence_info = indicators.get('divergence', {})
    if divergence_info.get('status') == 'success':
        conditions = divergence_info.get('conditions', {})
        print(f"   🎯 SKDJ超卖: {conditions.get('SKDJ超卖确认', False)}")
        print(f"   📊 双重背离: {conditions.get('双重背离', False)}")
        print(f"   💪 强趋势确认: {conditions.get('强趋势确认', False)}")
        print(f"   🚀 突破确认: {conditions.get('突破确认', False)}")
    else:
        print(f"   ⚠️ 背离检测: {divergence_info.get('status', '未知状态')}")

    # 显示运行模式
    if hasattr(C, 'use_dynamic_mode') and C.use_dynamic_mode:
        required_bars = max(C.ATRLength, C.VolLen)
        quality_level = get_data_quality_level(total_klines, required_bars)
        position_ratio = get_dynamic_position_ratio(total_klines, required_bars, 1.0)
        print(f"   🔄 运行模式: 动态模式 ({quality_level})")
        print(f"   📊 仓位调整: {position_ratio:.1%} (基于数据质量)")
        print(f"   📈 指标周期: ATR={indicators.get('atr_period_used', 'N/A')}, 成交量={indicators.get('vol_period_used', 'N/A')}")
    else:
        print(f"   ✅ 运行模式: 标准模式")

    # 数据管理状态
    max_klines = C.max_history_bars + 20  # 与滑动窗口保持一致
    if total_klines >= max_klines:
        print(f"   ✅ 数据管理: 滑动窗口模式 (最大{max_klines}个K线)")
    else:
        print(f"   📊 数据管理: 积累模式 ({total_klines}/{max_klines})")

    # 滑动窗口状态显示
    if hasattr(C, 'sliding_window_stats'):
        stats = C.sliding_window_stats
        print(f"   🔄 滑动窗口: 已清理{stats['cleanup_count']}次，累计移除{stats['total_removed']}个K线")

    if C.position == 1:
        current_price = indicators['current_price']
        profit_pct = (current_price - C.entry_price) / C.entry_price

        # 获取动态止盈止损信息
        divergence_result = indicators.get('divergence')
        if divergence_result and divergence_result['status'] == 'success':
            VAE_info = divergence_result.get('VAE_info', {})
            动态止盈比例 = VAE_info.get('动态止盈比例', 1.5)
            动态止损比例 = VAE_info.get('动态止损比例', 1.0)
            实际波动幅度 = VAE_info.get('实际波动幅度', 1.5)
            波动率区间 = VAE_info.get('波动率区间', '正常波动区')

            print(f"   💵 入场价格: {C.entry_price:.3f}")
            print(f"   💰 当前价格: {current_price:.3f}")
            print(f"   📈 浮动盈亏: {profit_pct:.2%}")
            print(f"   🌊 市场波动: {实际波动幅度:.2f}% ({波动率区间})")
            print(f"   🎯 动态止盈: {动态止盈比例:.2f}% (基于市场波动)")
            print(f"   🛡️ 动态止损: {动态止损比例:.2f}% (基于市场波动)")
            print(f"   ⚠️ 固定止损: {C.固定止损:.1f}% (兜底保护)")

            # 移动止盈信息显示
            if C.use_trailing_stop:
                # 计算基于波动率的动态回撤比例
                if 波动率区间 == '低波动区':
                    dynamic_trailing_ratio = C.trailing_stop_ratio * 0.5
                elif 波动率区间 == '高波动区':
                    dynamic_trailing_ratio = C.trailing_stop_ratio * 1.5
                elif 波动率区间 == '极高波动区':
                    dynamic_trailing_ratio = C.trailing_stop_ratio * 2.0
                else:  # 正常波动区
                    dynamic_trailing_ratio = C.trailing_stop_ratio

                # 检查移动止盈启动条件
                min_profit_to_start = 动态止损比例 / 100
                trailing_active = profit_pct > min_profit_to_start

                print(f"   🎯 移动止盈: 启用 (智能保护)")
                print(f"   📈 入场后最高: {C.highest_price_since_entry:.3f}")
                print(f"   💡 启动条件: 盈利>{min_profit_to_start:.1%} ({'已满足' if trailing_active else '未满足'})")

                if trailing_active:
                    # 计算智能保护信息
                    min_protection_line = C.entry_price * (1 + 动态止损比例 / 100)
                    current_profit_pct_value = profit_pct * 100

                    if current_profit_pct_value < 1.0:
                        protection_level = "严格保护"
                    elif current_profit_pct_value < 2.0:
                        protection_level = "适中保护"
                    else:
                        protection_level = "标准保护"

                    print(f"   🛡️ 保护模式: {protection_level} (最低保证{动态止损比例:.1f}%盈利)")

                    if C.trailing_stop_price > C.entry_price:
                        protected_profit = (C.trailing_stop_price - C.entry_price) / C.entry_price * 100
                        print(f"   🔄 移动止盈线: {C.trailing_stop_price:.3f} (保护{protected_profit:.1f}%利润)")
                    else:
                        print(f"   🔄 移动止盈线: {C.trailing_stop_price:.3f}")
                else:
                    print(f"   🔄 移动止盈线: 等待盈利达到启动条件")

                print(f"   📉 基础回撤比例: {dynamic_trailing_ratio:.1%} ({波动率区间})")
            else:
                print(f"   🎯 移动止盈: 禁用")

        else:
            print(f"   💵 入场价格: {C.entry_price:.3f}")
            print(f"   💰 当前价格: {current_price:.3f}")
            print(f"   📈 浮动盈亏: {profit_pct:.2%}")
            print(f"   🎯 止盈目标: {C.take_profit_pct:.1%}")
            print(f"   🛡️ 止损线: {C.stop_loss_pct:.1%}")

            # 移动止盈信息显示（简化版）
            if C.use_trailing_stop:
                print(f"   🎯 移动止盈: 启用 (回撤{C.trailing_stop_ratio:.1%})")
                print(f"   📈 入场后最高: {C.highest_price_since_entry:.3f}")
                if C.trailing_stop_price > 0:
                    print(f"   🔄 移动止盈线: {C.trailing_stop_price:.3f}")
                else:
                    print(f"   🔄 移动止盈线: 等待价格上涨后设置")

    # 显示CMF+BIAS双重背离策略状态
    print_divergence_signals_summary(C, indicators)

def check_entry_conditions(C, indicators, available_cash, bar_time):
    """
    CMF+BIAS双重背离策略开仓条件检查 - 基于5层过滤的多重确认开仓

    核心逻辑：
    1. SKDJ超卖确认（K<20且D<20）
    2. 双重背离确认（CMF底背离 + BIAS底背离）
    3. 强趋势确认（ADX>40）
    4. 突破确认（阻力线突破）
    """
    current_close = indicators['current_price']

    # 检查综合交易信号
    divergence_result = indicators.get('divergence')
    if not divergence_result or divergence_result['status'] != 'success':
        print(f"⚪ 开仓条件不满足 - 综合信号检测失败: {divergence_result.get('status', 'unknown') if divergence_result else 'no_data'}")
        return

    # 获取各项条件
    buy_signal = divergence_result.get('buy_signal', False)
    conditions = divergence_result.get('conditions', {})
    indicators_data = divergence_result.get('indicators', {})

    SKDJ超卖 = conditions.get('SKDJ超卖', False)
    CMF底背离 = conditions.get('CMF底背离', False)
    BIAS底背离 = conditions.get('BIAS底背离', False)
    双重背离 = conditions.get('双重背离', False)
    强趋势确认 = conditions.get('强趋势确认', False)
    突破确认 = conditions.get('突破确认', False)

    # 动态模式信号强度检查
    signal_strength_ok = should_trade_in_dynamic_mode(C, signal_strength_threshold=0.6)

    print(f"🔍 CMF+BIAS双重背离策略开仓条件检查:")
    print(f"   📊 SKDJ超卖: {SKDJ超卖} (K={indicators_data.get('SKDJ_K', 0):.1f}, D={indicators_data.get('SKDJ_D', 0):.1f})")
    print(f"   💰 CMF底背离: {CMF底背离} (CMF={indicators_data.get('CMF', 0):.3f})")
    print(f"   📈 BIAS底背离: {BIAS底背离} (BIAS={indicators_data.get('BIAS', 0):.2f}%)")
    print(f"   🔄 双重背离: {双重背离}")
    print(f"   💪 强趋势确认: {强趋势确认} (ADX={indicators_data.get('ADX', 0):.1f})")
    print(f"   🚀 突破确认: {突破确认} (价格={indicators_data.get('current_price', 0):.3f}, 阻力线={indicators_data.get('resistance_line', 0):.3f})")

    # 显示动态模式信息
    if hasattr(C, 'use_dynamic_mode') and C.use_dynamic_mode:
        required_bars = max(C.CMF_N, C.BIAS_N, C.ADX_N, C.VAE_周期 * 2) + 10
        current_bars = len(C.merged_klines)
        signal_strength = get_dynamic_signal_strength(current_bars, required_bars)
        print(f"   🔄 信号强度: {signal_strength_ok} (强度{signal_strength:.1%}, 阈值60%)")

    # 综合判断开仓条件 - 使用新的买入信号逻辑
    final_entry_decision = (buy_signal and signal_strength_ok)

    print(f"   🎯 综合买入信号: {buy_signal}")
    print(f"   ✅ 最终开仓决策: {final_entry_decision}")

    if final_entry_decision:
        print(f"✅ 满足开仓条件 - 执行CMF+BIAS双重背离买入策略")
        execute_buy_order(C, indicators, available_cash, current_close, bar_time)
    else:
        if not buy_signal:
            print("⚪ 开仓条件不满足 - 综合买入信号未触发")
            if not SKDJ超卖:
                print("   ❌ SKDJ未达到超卖状态")
            if not 双重背离:
                print("   ❌ 双重背离条件未满足")
            if not 强趋势确认:
                print("   ❌ ADX趋势强度不足")
            if not 突破确认:
                print("   ❌ 阻力线突破未确认")
        elif not signal_strength_ok:
            print("⚪ 开仓条件不满足 - 动态模式信号强度不足")
        else:
            print("⚪ 开仓条件不满足 - 其他条件不足")

def execute_buy_order(C, indicators, available_cash, prev_resistance, bar_time):
    """
    挂单模式买入订单执行 - 加价买入策略

    优化点：
    1. 移除复杂的委托管理检查
    2. 使用限价单挂单模式（price_type=11）
    3. 买入时加价挂单：高于当前价格的限价单，确保成交
    4. 直接使用QMT passorder函数
    5. 添加撤单功能，避免资金占用
    """
    try:
        # ============================================================================
        # 步骤1：检查是否有未处理订单，防止重复挂单
        # ============================================================================
        print("� 检查是否有未处理的买入订单...")

        # 先检查是否有未处理订单
        has_pending = check_has_pending_orders(C, order_type='BUY')
        if has_pending:
            print("⚠️ 发现未处理的买入订单，尝试撤销...")
            cancel_stats = cancel_pending_orders(C, order_type='BUY')

            print(f"📊 撤单统计: 检查{cancel_stats['total_checked']}个订单, "
                  f"撤销{cancel_stats['cancelled_count']}个, "
                  f"成功{cancel_stats['cancel_success']}个, "
                  f"失败{cancel_stats['cancel_failed']}个")

            # 如果撤单失败，直接返回，避免重复挂单
            if cancel_stats['has_pending_orders'] or cancel_stats['cancel_failed'] > 0:
                print("❌ 撤单未完全成功，跳过本次买入以避免重复挂单")
                return

            # 撤单成功后等待资金释放
            import time
            time.sleep(2)
            print("✅ 撤单完成，资金已释放")
        else:
            print("✅ 没有未处理的买入订单，可以继续下单")

        # ============================================================================
        # 步骤2：执行新的买入订单 - 加价挂单模式
        # ============================================================================
        current_close = indicators['closes'][-1]

        # 挂单价格计算：加价挂单（高于当前价格）
        # 使用价格偏移量确保成交，愿意付出价格代价
        price_offset_ratio = getattr(C, 'buy_hang_offset_ratio', 0.002)  # 默认0.2%向上偏移
        hang_price = current_close * (1 + price_offset_ratio)
        hang_price = round(hang_price, 2)  # 保留2位小数

        # 动态资金管理 - 支持动态仓位调整
        base_ratio = 0.9  # 基础资金使用比例

        # 动态模式下调整资金使用比例
        if hasattr(C, 'use_dynamic_mode') and C.use_dynamic_mode:
            required_bars = max(C.ATRLength, C.VolLen)
            current_bars = len(C.merged_klines)
            position_ratio = get_dynamic_position_ratio(current_bars, required_bars, base_ratio)
            usable_cash = available_cash * position_ratio
            print(f"🔄 动态资金管理: 使用{position_ratio:.1%}资金 (基于{current_bars}/{required_bars}数据)")
        else:
            usable_cash = available_cash * base_ratio  # 标准模式

        vol = int(usable_cash / hang_price / 10) * 10  # 10股整数倍（1手=10股）

        if vol < 10:
            print(f"⚠️ 资金不足，无法买入最小单位 (需要: {hang_price * 10:.2f}, 可用: {usable_cash:.2f})")
            return

        print(f"💰 加价买入资金管理:")
        print(f"   可用资金: {available_cash:.2f}")
        print(f"   当前价格: {current_close:.3f}")
        print(f"   挂单价格: {hang_price:.3f} (向上偏移{price_offset_ratio*100:.2f}% - 加价买入)")
        print(f"   买入数量: {vol}股")
        print(f"   预计成本: {hang_price * vol:.2f}")

        # 加价挂单下单（使用限价单）
        msg = f"RRL {C.stock} 突破加价买入 {vol}股@{hang_price:.3f}"

        # 初始化下单结果
        order_success = False

        try:
            # 使用限价单挂单模式
            passorder(
                23,           # 买入代码
                1101,         # 委托类型
                C.acct,       # 账户ID
                C.stock,      # 股票代码
                11,           # 限价单（挂单模式）
                hang_price,   # 挂单价格
                vol,          # 数量
                'RRL',        # 策略名称（修改为RRL）
                1,            # 立即下单
                msg,          # 委托备注
                C             # 上下文
            )

            print(f"📤 QMT买入下单已提交: {msg}")

            # 根据QMT官方文档，passorder成功提交后直接更新状态
            # 实际成交状态通过事件回调或get_last_order_id查询
            order_success = True

        except NameError:
            # QMT环境未就绪，模拟成功（用于测试）
            print("⚠️ QMT环境未就绪，模拟下单成功")
            order_success = True
        except Exception as e:
            print(f"❌ 下单异常: {e}")
            order_success = False

        if order_success:
            # 下单提交成功，更新策略状态
            # 注意：这里是下单提交成功，不是成交成功
            C.position = 1
            C.entry_price = current_close
            C.exit_price = current_close * (1 + C.take_profit_pct)  # 基于百分比的止盈价格
            C.bars_since_entry = 0
            C.last_trade_time = bar_time
            C.total_trades += 1
            C.successful_trades += 1

            # 初始化移动止盈相关变量
            C.highest_price_since_entry = current_close  # 入场价格作为初始最高价
            C.trailing_stop_price = 0  # 移动止盈线初始为0，等待价格上涨后设置

            print(f"✅ {msg}")
            print(f"🎯 止盈价格: {C.exit_price:.3f} (+{C.take_profit_pct:.1%})")
            print(f"🛡️ 止损价格: {current_close * (1 - C.stop_loss_pct):.3f} (-{C.stop_loss_pct:.1%})")
            print(f"📊 持仓状态已更新: position={C.position}, entry_price={C.entry_price:.3f}")

            # 移动止盈初始化信息
            if C.use_trailing_stop:
                print(f"🎯 移动止盈已启用: 回撤比例={C.trailing_stop_ratio:.1%}")
                print(f"   📈 初始最高价: {C.highest_price_since_entry:.3f}")
                print(f"   🔄 移动止盈线: 等待价格上涨后设置")

            # 可选：查询订单ID用于后续跟踪
            try:
                order_id = get_last_order_id(C.acct, 'STOCK', 'ORDER')
                if order_id != '-1':
                    print(f"📋 订单ID: {order_id}")
                else:
                    print("⚠️ 暂未获取到订单ID（可能需要等待）")
            except:
                print("⚠️ 无法查询订单ID（QMT环境限制）")
        else:
            print(f"❌ 买入下单提交失败: {msg}")
            C.failed_trades += 1

    except Exception as e:
        print(f"❌ 买入执行错误: {e}")
        C.failed_trades += 1

def check_exit_conditions(C, indicators, current_position, bar_time):
    """
    CMF+BIAS双重背离策略平仓条件检查 - 基于实际市场波动的动态止盈止损 + 移动止盈

    核心逻辑：
    1. 移动止盈功能（基于市场波动的动态回撤比例）
    2. 基于实际市场波动幅度的动态止盈止损（根据ATR/价格比例自适应调整）
    3. 固定止损保护（兜底机制）
    4. 超过最大持仓时间
    5. 可选的卖出信号确认
    """
    current_close = indicators['current_price']

    # 计算盈亏比例
    profit_pct = (current_close - C.entry_price) / C.entry_price

    # 获取VAE动态风控信息
    divergence_result = indicators.get('divergence')
    VAE_info = {}
    sell_signal = False

    if divergence_result and divergence_result['status'] == 'success':
        VAE_info = divergence_result.get('VAE_info', {})
        sell_signal = divergence_result.get('sell_signal', False)

    # 获取基于市场波动的动态风控参数
    波动率区间 = VAE_info.get('波动率区间', '正常波动区')
    波动率比值 = VAE_info.get('波动率比值', 1.0)
    实际波动幅度 = VAE_info.get('实际波动幅度', 1.5)
    动态止盈比例 = VAE_info.get('动态止盈比例', 1.5)
    动态止损比例 = VAE_info.get('动态止损比例', 1.0)

    # 保留原有动态TR作为备用
    动态TR = VAE_info.get('动态TR', C.VAE_基础TR)

    # ============================================================================
    # 移动止盈功能 - 基于市场波动的动态回撤比例
    # ============================================================================
    trailing_stop_triggered = False
    trailing_stop_reason = ""
    dynamic_trailing_ratio = C.trailing_stop_ratio  # 默认回撤比例

    if C.use_trailing_stop and C.position == 1:
        # 更新入场后最高价格
        if current_close > C.highest_price_since_entry:
            C.highest_price_since_entry = current_close

        # 计算基于市场波动的动态回撤比例
        # 在高波动市场使用更大的回撤比例，在低波动市场使用更小的回撤比例
        if 波动率区间 == '低波动区':
            dynamic_trailing_ratio = C.trailing_stop_ratio * 0.5  # 低波动：1%回撤
        elif 波动率区间 == '正常波动区':
            dynamic_trailing_ratio = C.trailing_stop_ratio  # 正常波动：2%回撤
        elif 波动率区间 == '高波动区':
            dynamic_trailing_ratio = C.trailing_stop_ratio * 1.5  # 高波动：3%回撤
        else:  # 极高波动区
            dynamic_trailing_ratio = C.trailing_stop_ratio * 2.0  # 极高波动：4%回撤

        # 移动止盈只在盈利状态下启动
        # 计算当前盈利比例，只有盈利超过动态止损比例时才启动移动止盈
        min_profit_to_start = 动态止损比例 / 100  # 至少要盈利超过动态止损比例才启动

        if profit_pct > min_profit_to_start:
            # ============================================================================
            # 智能移动止盈算法 - 防止盈利完全损失
            # ============================================================================

            # 1. 计算最小保护线：确保至少保护VAE动态止损比例的盈利
            min_protection_line = C.entry_price * (1 + 动态止损比例 / 100)

            # 2. 根据当前盈利水平动态调整回撤比例
            current_profit_pct_value = profit_pct * 100  # 转换为百分比数值

            if current_profit_pct_value < 1.0:  # 盈利小于1%
                # 小盈利时使用更严格的回撤控制
                effective_trailing_ratio = min(dynamic_trailing_ratio * 0.3, current_profit_pct_value * 0.5 / 100)
            elif current_profit_pct_value < 2.0:  # 盈利1-2%
                # 中等盈利时使用适中的回撤控制
                effective_trailing_ratio = dynamic_trailing_ratio * 0.6
            else:  # 盈利大于2%
                # 大盈利时使用完整回撤比例
                effective_trailing_ratio = dynamic_trailing_ratio

            # 3. 计算基于回撤的移动止盈线
            trailing_based_line = C.highest_price_since_entry * (1 - effective_trailing_ratio)

            # 4. 最终移动止盈线：取回撤线和最小保护线的最大值
            new_trailing_stop_price = max(trailing_based_line, min_protection_line)

            # 5. 移动止盈线只能向上移动（朝有利方向）
            if new_trailing_stop_price > C.trailing_stop_price:
                C.trailing_stop_price = new_trailing_stop_price

            # 6. 检查是否触发移动止盈
            if C.trailing_stop_price > C.entry_price and current_close <= C.trailing_stop_price:
                trailing_stop_triggered = True
                actual_profit = (C.trailing_stop_price - C.entry_price) / C.entry_price * 100
                trailing_stop_reason = f"智能移动止盈触发({波动率区间},有效回撤{effective_trailing_ratio:.1%},保护{actual_profit:.1f}%利润)"
        else:
            # 盈利不足，移动止盈未启动
            C.trailing_stop_price = 0

    # 平仓条件检查 - 使用基于市场波动的新逻辑
    # 1. 基于实际市场波动的动态止盈
    market_based_take_profit_exit = profit_pct >= (动态止盈比例 / 100)

    # 2. 基于实际市场波动的动态止损
    market_based_stop_loss_exit = profit_pct <= -(动态止损比例 / 100)

    # 3. 固定止损保护（兜底机制）
    fixed_stop_loss_exit = profit_pct <= -(C.固定止损 / 100)

    print(f"🔍 CMF+BIAS双重背离策略平仓条件检查 (基于市场波动 + 移动止盈):")
    print(f"   💰 当前价格: {current_close:.3f}")
    print(f"   📊 入场价格: {C.entry_price:.3f}")
    print(f"   📈 盈亏比例: {profit_pct:.2%}")
    print(f"   🌊 波动率区间: {波动率区间} (比值: {波动率比值:.2f})")
    print(f"   📊 实际波动幅度: {实际波动幅度:.2f}%")

    # 移动止盈信息显示
    if C.use_trailing_stop and C.position == 1:
        min_profit_to_start = 动态止损比例 / 100
        trailing_active = profit_pct > min_profit_to_start

        print(f"   🎯 移动止盈状态: {'启用' if C.use_trailing_stop else '禁用'}")
        print(f"   📈 入场后最高价: {C.highest_price_since_entry:.3f}")
        print(f"   💡 启动条件: 盈利>{min_profit_to_start:.1%} ({'已满足' if trailing_active else '未满足'})")

        if trailing_active:
            # 计算智能移动止盈的详细信息
            min_protection_line = C.entry_price * (1 + 动态止损比例 / 100)
            current_profit_pct_value = profit_pct * 100

            # 计算有效回撤比例
            if current_profit_pct_value < 1.0:
                effective_trailing_ratio = min(dynamic_trailing_ratio * 0.3, current_profit_pct_value * 0.5 / 100)
                protection_level = "严格保护"
            elif current_profit_pct_value < 2.0:
                effective_trailing_ratio = dynamic_trailing_ratio * 0.6
                protection_level = "适中保护"
            else:
                effective_trailing_ratio = dynamic_trailing_ratio
                protection_level = "标准保护"

            print(f"   🛡️ 最小保护线: {min_protection_line:.3f} (保证{动态止损比例:.1f}%盈利)")
            print(f"   📉 有效回撤比例: {effective_trailing_ratio:.1%} ({protection_level})")

            if C.trailing_stop_price > C.entry_price:
                protected_profit = (C.trailing_stop_price - C.entry_price) / C.entry_price * 100
                print(f"   🔄 移动止盈线: {C.trailing_stop_price:.3f} (保护{protected_profit:.1f}%利润)")
            else:
                print(f"   🔄 移动止盈线: {C.trailing_stop_price:.3f}")
        else:
            print(f"   🔄 移动止盈线: 等待盈利达到启动条件")

        print(f"   📉 基础回撤比例: {dynamic_trailing_ratio:.1%} ({波动率区间})")
        print(f"   ⚡ 移动止盈触发: {trailing_stop_triggered}")

    print(f"   🎯 市场动态止盈: {market_based_take_profit_exit} (目标: {动态止盈比例:.2f}%)")
    print(f"   🛡️ 市场动态止损: {market_based_stop_loss_exit} (目标: {动态止损比例:.2f}%)")
    print(f"   ⚠️ 固定止损保护: {fixed_stop_loss_exit} (目标: {C.固定止损:.1f}%)")
    print(f"   📉 卖出信号: {sell_signal}")

    # 显示新旧逻辑对比
    old_take_profit = profit_pct >= (动态TR / 100)
    old_stop_loss = profit_pct <= -(C.VAE_初始止损 / 100)
    print(f"   📋 对比-旧止盈逻辑: {old_take_profit} (目标: {动态TR:.1f}%)")
    print(f"   📋 对比-旧止损逻辑: {old_stop_loss} (目标: {C.VAE_初始止损:.1f}%)")

    # 执行平仓决策 - 优先级：固定止损 > 市场动态止损 > 移动止盈 > 市场动态止盈 > 卖出信号
    if fixed_stop_loss_exit:
        execute_sell_order(C, current_close, current_position, "固定止损保护平仓", bar_time)
    elif market_based_stop_loss_exit:
        execute_sell_order(C, current_close, current_position, f"市场动态止损平仓({波动率区间},{动态止损比例:.2f}%)", bar_time)
    elif trailing_stop_triggered:
        execute_sell_order(C, current_close, current_position, trailing_stop_reason, bar_time)
    elif market_based_take_profit_exit:
        execute_sell_order(C, current_close, current_position, f"市场动态止盈平仓({波动率区间},{动态止盈比例:.2f}%)", bar_time)
    elif sell_signal:
        execute_sell_order(C, current_close, current_position, "卖出信号平仓", bar_time)
    else:
        print(f"🟡 继续持仓 (盈亏: {profit_pct:.2%}, 第{C.bars_since_entry}根K线)")
        print(f"   💡 当前需要盈利{动态止盈比例:.2f}%才会止盈，亏损{动态止损比例:.2f}%才会止损")

        if C.use_trailing_stop:
            min_profit_to_start = 动态止损比例 / 100
            if profit_pct > min_profit_to_start and C.trailing_stop_price > C.entry_price:
                protected_profit = (C.trailing_stop_price - C.entry_price) / C.entry_price * 100
                min_protection_line = C.entry_price * (1 + 动态止损比例 / 100)

                # 显示智能保护信息
                current_profit_pct_value = profit_pct * 100
                if current_profit_pct_value < 1.0:
                    protection_mode = "严格保护"
                elif current_profit_pct_value < 2.0:
                    protection_mode = "适中保护"
                else:
                    protection_mode = "标准保护"

                print(f"   🎯 智能移动止盈: {C.trailing_stop_price:.3f} (保护{protected_profit:.1f}%利润)")
                print(f"   🛡️ 保护模式: {protection_mode} (最低保证{动态止损比例:.1f}%盈利)")
            elif profit_pct > 0:
                print(f"   🎯 移动止盈: 等待盈利达到{min_profit_to_start:.1%}后启动 (当前{profit_pct:.1%})")
            else:
                print(f"   🎯 移动止盈: 未启动 (当前亏损{abs(profit_pct):.1%})")

def execute_sell_order(C, current_close, current_position, reason, bar_time):
    """
    挂单模式卖出订单执行 - 降价卖出策略

    优化点：
    1. 移除复杂的委托管理检查
    2. 使用限价单挂单模式（price_type=11）
    3. 卖出时降价挂单：低于当前价格的限价单，确保成交
    4. 直接使用QMT passorder函数
    5. 添加撤单功能，避免持仓占用
    """
    try:
        # ============================================================================
        # 步骤1：检查是否有未处理订单，防止重复挂单
        # ============================================================================
        print("� 检查是否有未处理的卖出订单...")

        # 先检查是否有未处理订单
        has_pending = check_has_pending_orders(C, order_type='SELL')
        if has_pending:
            print("⚠️ 发现未处理的卖出订单，尝试撤销...")
            cancel_stats = cancel_pending_orders(C, order_type='SELL')

            print(f"📊 撤单统计: 检查{cancel_stats['total_checked']}个订单, "
                  f"撤销{cancel_stats['cancelled_count']}个, "
                  f"成功{cancel_stats['cancel_success']}个, "
                  f"失败{cancel_stats['cancel_failed']}个")

            # 如果撤单失败，直接返回，避免重复挂单
            if cancel_stats['has_pending_orders'] or cancel_stats['cancel_failed'] > 0:
                print("❌ 撤单未完全成功，跳过本次卖出以避免重复挂单")
                return

            # 撤单成功后等待持仓释放
            import time
            time.sleep(2)
            print("✅ 撤单完成，持仓已释放")
        else:
            print("✅ 没有未处理的卖出订单，可以继续下单")

        # ============================================================================
        # 步骤2：执行新的卖出订单 - 降价挂单模式
        # ============================================================================

        # 挂单价格计算：降价挂单（低于当前价格）
        # 使用价格偏移量确保成交，愿意付出价格代价
        price_offset_ratio = getattr(C, 'sell_hang_offset_ratio', 0.002)  # 默认0.2%向下偏移
        hang_price = current_close * (1 - price_offset_ratio)
        hang_price = round(hang_price, 2)  # 保留2位小数

        print(f"💼 降价卖出准备:")
        print(f"   平仓原因: {reason}")
        print(f"   当前价格: {current_close:.3f}")
        print(f"   挂单价格: {hang_price:.3f} (向下偏移{price_offset_ratio*100:.2f}% - 降价卖出)")
        print(f"   卖出数量: {current_position}股")
        print(f"   入场价格: {C.entry_price:.3f}")

        # 计算预期盈亏（基于挂单价格）
        expected_pnl = (hang_price - C.entry_price) * current_position
        pnl_ratio = (hang_price - C.entry_price) / C.entry_price * 100
        print(f"   预期盈亏: {expected_pnl:.2f} ({pnl_ratio:+.2f}%)")

        # 降价挂单下单（使用限价单）
        msg = f"RRL {C.stock} {reason} 降价卖出 {current_position}股@{hang_price:.3f}"

        # 初始化下单结果
        order_success = False

        try:
            # 使用限价单挂单模式
            passorder(
                24,           # 卖出代码
                1101,         # 委托类型
                C.acct,       # 账户ID
                C.stock,      # 股票代码
                11,           # 限价单（挂单模式）
                hang_price,   # 挂单价格
                current_position, # 数量
                'RRL',        # 策略名称（修改为RRL）
                1,            # 立即下单
                msg,          # 委托备注
                C             # 上下文
            )

            print(f"📤 QMT卖出下单已提交: {msg}")

            # 根据QMT官方文档，passorder成功提交后直接更新状态
            order_success = True

        except NameError:
            # QMT环境未就绪，模拟成功（用于测试）
            print("⚠️ QMT环境未就绪，模拟下单成功")
            order_success = True
        except Exception as e:
            print(f"❌ 下单异常: {e}")
            order_success = False

        if order_success:
            print(f"✅ {msg}")
            print(f"💰 预期盈亏: {expected_pnl:.2f} ({pnl_ratio:+.2f}%)")

            # 重置状态
            reset_position_state(C)
            C.successful_trades += 1

            # 可选：查询订单ID用于后续跟踪
            try:
                order_id = get_last_order_id(C.acct, 'STOCK', 'ORDER')
                if order_id != '-1':
                    print(f"� 订单ID: {order_id}")
                else:
                    print("⚠️ 暂未获取到订单ID（可能需要等待）")
            except:
                print("⚠️ 无法查询订单ID（QMT环境限制）")
        else:
            print(f"❌ 卖出下单提交失败: {msg}")
            C.failed_trades += 1

    except Exception as e:
        print(f"❌ 卖出执行错误: {e}")
        C.failed_trades += 1

# safe_passorder函数已被移除，现在直接使用QMT的passorder函数
# 这是基于官方示例最佳实践的优化，依赖QMT平台的可靠性

def reset_position_state(C):
    """
    重置持仓状态（包括移动止盈相关变量）
    """
    old_position = getattr(C, 'position', 0)
    old_entry_price = getattr(C, 'entry_price', 0)
    old_exit_price = getattr(C, 'exit_price', 0)
    old_highest_price = getattr(C, 'highest_price_since_entry', 0)
    old_trailing_stop = getattr(C, 'trailing_stop_price', 0)

    C.position = 0
    C.entry_price = 0
    C.exit_price = 0
    C.bars_since_entry = 0

    # 重置移动止盈相关变量
    C.highest_price_since_entry = 0
    C.trailing_stop_price = 0

    print(f"🔄 持仓状态已重置: {old_position}→{C.position}, 入场价:{old_entry_price:.3f}→{C.entry_price}, 止盈价:{old_exit_price:.3f}→{C.exit_price}")
    print(f"   🎯 移动止盈已重置: 最高价:{old_highest_price:.3f}→{C.highest_price_since_entry}, 止盈线:{old_trailing_stop:.3f}→{C.trailing_stop_price}")

def cancel_all_orders(C):
    """
    手动撤销所有未成交订单

    参数:
        C: 策略上下文

    返回:
        bool: 是否成功
    """
    try:
        print("🔄 开始撤销所有未成交订单...")
        cancel_stats = cancel_pending_orders(C, order_type='ALL')

        print(f"📊 撤单完成统计:")
        print(f"   检查订单数: {cancel_stats['total_checked']}")
        print(f"   撤销订单数: {cancel_stats['cancelled_count']}")
        print(f"   撤单成功数: {cancel_stats['cancel_success']}")
        print(f"   撤单失败数: {cancel_stats['cancel_failed']}")

        if cancel_stats['errors']:
            print(f"   错误信息: {cancel_stats['errors']}")

        return cancel_stats['cancel_success'] > 0 or cancel_stats['cancelled_count'] == 0

    except Exception as e:
        print(f"❌ 撤销所有订单异常: {e}")
        return False

def monitor_order_status(C):
    """
    监控当前委托状态 - 基于QMT官方API

    参数:
        C: 策略上下文
    """
    try:
        print("📋 监控委托状态...")
        order_info = check_order_status(C)

        if order_info['status'] == 'success':
            print(f"📊 委托状态报告:")
            print(f"   委托ID: {order_info['order_id']}")
            print(f"   状态: {order_info['status_desc']}")
            print(f"   方向: {order_info['op_type_desc']}")
            print(f"   股票: {order_info['stock']}")
            print(f"   总数量: {order_info['total_volume']}")
            print(f"   已成交: {order_info['traded_volume']}")
            print(f"   是否待成交: {order_info['is_pending']}")
            print(f"   是否已完成: {order_info['is_completed']}")
            print(f"   是否已撤销: {order_info['is_cancelled']}")
        elif order_info['status'] == 'no_order':
            print("📋 当前没有委托")
        else:
            print(f"❌ 查询委托状态失败: {order_info['message']}")

    except Exception as e:
        print(f"❌ 监控委托状态异常: {e}")

# check_order_cooldown函数已被移除
# 优化：依赖QMT平台的下单控制机制，简化自定义管理

# add_to_waiting_list函数已被移除
# 优化：使用事件回调机制替代手动委托列表管理

# check_pending_orders函数已被移除
# 优化：依赖QMT平台的委托管理机制，简化重复下单控制

# update_order_status函数已被移除
# 优化：使用事件回调机制自动处理订单状态更新

def merge_two_bars(data_list):
    """
    将两个数据合成为一个OHLC+V K线
    """
    if len(data_list) != 2:
        raise ValueError("需要恰好两个数据进行合成")

    data1, data2 = data_list

    # 计算OHLC
    open_price = data1['price']
    close_price = data2['price']
    high_price = max(data1['price'], data2['price'])
    low_price = min(data1['price'], data2['price'])
    total_volume = data1['volume'] + data2['volume']

    return {
        'start_time': data1['time'],
        'end_time': data2['time'],
        'open': round(open_price, 3),
        'high': round(high_price, 3),
        'low': round(low_price, 3),
        'close': round(close_price, 3),
        'volume': total_volume,
    }

def merge_three_bars(data_list):
    """
    将三个数据合成为一个OHLC+V K线
    """
    if len(data_list) != 3:
        raise ValueError("需要恰好三个数据进行合成")

    data1, data2, data3 = data_list

    # 计算OHLC
    open_price = data1['price']
    close_price = data3['price']
    high_price = max(data1['price'], data2['price'], data3['price'])
    low_price = min(data1['price'], data2['price'], data3['price'])
    total_volume = data1['volume'] + data2['volume'] + data3['volume']

    return {
        'start_time': data1['time'],
        'end_time': data3['time'],
        'open': round(open_price, 3),
        'high': round(high_price, 3),
        'low': round(low_price, 3),
        'close': round(close_price, 3),
        'volume': total_volume,
    }

# ============================================================================
# 备份原始函数（优化前版本，用于必要时回滚）
# ============================================================================

def execute_buy_order_original_backup(C, indicators, available_cash, prev_resistance, bar_time):
    """原始买入订单执行函数 - 备份版本"""
    # 备份原始实现，此函数已被优化版本替代
    pass

def execute_sell_order_original_backup(C, current_close, current_position, reason, bar_time):
    """原始卖出订单执行函数 - 备份版本"""
    # 备份原始实现，此函数已被优化版本替代
    pass

def safe_passorder_original_backup(order_type, price_type, accountid, stock, direction, offset, volume, context, action_name):
    """原始安全下单函数 - 备份版本"""
    # 备份原始实现，此函数已被优化版本替代
    pass

# ============================================================================
# 修复版预热数据加载模块 - 使用参考文件API格式，确保数据一致性
# ============================================================================

def get_higher_frequency_period(target_period):
    """
    根据目标周期获取更高频率的周期
    """
    period_map = {
        '1h': '15m',
        '30m': '5m',
        '15m': '1m',
        '5m': '1m',
        '1m': '1m'  # 1分钟已经是最小单位
    }
    return period_map.get(target_period, '1m')

def get_tick_level_historical_data_fixed(C, required_klines):
    """
    获取tick级别的历史数据用于合成 - 使用参考文件API格式

    参数:
        C: 策略上下文
        required_klines: 需要的合成K线数量

    返回:
        list: tick级别的数据列表
    """
    try:
        # 需要获取的tick数量 = 需要的K线数量 × 2（因为2个tick合成1个K线）
        required_ticks = required_klines * 2 + 20  # 多获取一些作为缓冲

        print(f"📊 获取tick级历史数据: {required_ticks}个数据点")

        # 方案1: 尝试获取tick数据 - 使用参考文件的API格式
        tick_data = None
        try:
            tick_data = C.get_market_data_ex(
                ['lastPrice', 'volume'],    # 字段列表（数组格式）
                [C.stock],                  # 股票列表（数组格式）
                period='tick',
                count=required_ticks,
                subscribe=False
            )
        except Exception as e:
            print(f"   ⚠️ tick数据获取失败: {e}")
            tick_data = None

        # 方案2: 如果tick数据不可用，尝试获取更高频率的K线数据
        if not tick_data or C.stock not in tick_data:
            print("   🔄 tick数据不可用，尝试获取高频K线数据")

            high_freq_period = get_higher_frequency_period(C.period)
            try:
                tick_data = C.get_market_data_ex(
                    ['close', 'volume'],       # 字段列表（数组格式）
                    [C.stock],                 # 股票列表（数组格式）
                    period=high_freq_period,
                    count=required_ticks,
                    subscribe=False
                )
            except Exception as e:
                print(f"   ❌ 高频K线数据获取失败: {e}")
                return []

        if not tick_data or C.stock not in tick_data:
            print("   ❌ 无法获取任何高频历史数据")
            return []

        # 转换为tick格式 - 使用参考文件的数据处理方式
        stock_data = tick_data[C.stock]
        tick_list = []

        # 处理tick数据或高频K线数据
        if 'lastPrice' in stock_data:
            # 真实tick数据
            prices = stock_data['lastPrice'].values.astype(float) if hasattr(stock_data['lastPrice'], 'values') else [float(x) for x in stock_data['lastPrice']]
            volumes = stock_data['volume'].values.astype(float) if hasattr(stock_data['volume'], 'values') else [float(x) for x in stock_data['volume']]
        else:
            # 高频K线数据，使用收盘价作为tick价格
            prices = stock_data['close'].values.astype(float) if hasattr(stock_data['close'], 'values') else [float(x) for x in stock_data['close']]
            volumes = stock_data['volume'].values.astype(float) if hasattr(stock_data['volume'], 'values') else [float(x) for x in stock_data['volume']]

        # 转换为tick格式 - 直接使用增量成交量
        for i in range(len(prices)):
            current_volume = volumes[i]

            # 直接使用增量成交量 - tick数据确定为增量
            volume_increment = max(1, current_volume)  # 确保至少为1

            # 基本的异常值检测
            if current_volume > 100000:  # 如果单个tick成交量超过10万，可能异常
                volume_increment = min(current_volume, 10000)  # 限制在1万以内
                print(f"      ⚠️ 异常大成交量: {current_volume:.0f}, 修正为: {volume_increment:.0f}")
            elif current_volume <= 0:
                volume_increment = 1  # 最小保护
                print(f"      ⚠️ 零成交量, 修正为: {volume_increment:.0f}")

            # 创建tick数据点
            tick_data_point = {
                'time': f"历史tick_{i}",
                'price': prices[i],
                'volume': volume_increment,
                'data_quality': 'reliable'
            }
            tick_list.append(tick_data_point)

        print(f"   ✅ 获取到{len(tick_list)}个tick数据点")
        return tick_list

    except Exception as e:
        print(f"   ❌ 获取tick级数据失败: {e}")
        return []

# ============================================================================
# 动态数据积累功能模块 - 替代数据预热功能
# ============================================================================

def try_initialize_historical_data(C):
    """
    尝试初始化历史数据（可选，失败时启用动态模式）

    参数:
        C: 策略上下文

    返回:
        bool: 是否成功初始化
    """
    try:
        # 尝试获取一些历史K线数据用于K线合成
        print(f"   📊 尝试获取历史数据进行K线合成...")

        # 这里可以尝试获取历史数据，但不强制要求成功
        # 如果失败，将启用动态模式

        # 暂时返回False，启用动态模式
        # 实际使用时可以尝试调用历史数据API
        return False

    except Exception as e:
        print(f"   ⚠️ 历史数据获取失败: {e}")
        return False

def get_data_quality_level(current_count, required_count):
    """
    获取数据质量等级

    参数:
        current_count: 当前数据数量
        required_count: 需要的数据数量

    返回:
        str: 质量等级描述
    """
    ratio = current_count / required_count

    if ratio >= 1.0:
        return "高质量 (完整数据)"
    elif ratio >= 0.7:
        return "中等质量 (70%+数据)"
    elif ratio >= 0.3:
        return "低质量 (30%+数据)"
    else:
        return "极低质量 (<30%数据)"

def get_dynamic_position_ratio(current_count, required_count, base_ratio=1.0):
    """
    根据数据质量动态调整仓位比例

    参数:
        current_count: 当前数据数量
        required_count: 需要的数据数量
        base_ratio: 基础仓位比例

    返回:
        float: 调整后的仓位比例
    """
    ratio = current_count / required_count

    if ratio >= 1.0:
        return base_ratio  # 完整数据，使用正常仓位
    elif ratio >= 0.7:
        return base_ratio * 0.8  # 70%+数据，使用80%仓位
    elif ratio >= 0.3:
        return base_ratio * 0.5  # 30%+数据，使用50%仓位
    else:
        return base_ratio * 0.3  # <30%数据，使用30%仓位

def get_dynamic_signal_strength(current_count, required_count):
    """
    根据数据质量评估交易信号强度

    参数:
        current_count: 当前数据数量
        required_count: 需要的数据数量

    返回:
        float: 信号强度系数 (0.3-1.0)
    """
    ratio = current_count / required_count

    if ratio >= 1.0:
        return 1.0  # 完整数据，信号强度100%
    elif ratio >= 0.7:
        return 0.9  # 70%+数据，信号强度90%
    elif ratio >= 0.5:
        return 0.8  # 50%+数据，信号强度80%
    elif ratio >= 0.3:
        return 0.6  # 30%+数据，信号强度60%
    else:
        return 0.4  # <30%数据，信号强度40%

def should_trade_in_dynamic_mode(C, signal_strength_threshold=0.6):
    """
    判断在动态模式下是否应该进行交易

    参数:
        C: 策略上下文
        signal_strength_threshold: 信号强度阈值

    返回:
        bool: 是否可以交易
    """
    if not hasattr(C, 'use_dynamic_mode') or not C.use_dynamic_mode:
        return True  # 标准模式，正常交易

    required_bars = max(C.ATRLength, C.VolLen)
    current_bars = len(C.merged_klines)
    signal_strength = get_dynamic_signal_strength(current_bars, required_bars)

    return signal_strength >= signal_strength_threshold

# ============================================================================
# 原有数据预热功能（已废弃，保留用于参考）
# ============================================================================

def preload_historical_data_with_merge_fixed(C):
    """
    使用与主策略相同的合成逻辑预加载历史数据 - 修复版

    参数:
        C: 策略上下文

    返回:
        bool: 是否成功预加载
    """
    print(f"\n🔥 开始预热数据加载 - 使用修复版合成逻辑")
    print(f"   目标: {C.stock}, 周期: {C.period}")

    try:
        # 计算需要的K线数量
        required_klines = max(C.ATRLength, C.VolLen) + 10  # 多加载一些作为缓冲

        # 步骤1: 获取tick级别的历史数据
        tick_data_list = get_tick_level_historical_data_fixed(C, required_klines)

        if len(tick_data_list) < required_klines:
            print(f"   ⚠️ tick数据不足: {len(tick_data_list)} < {required_klines}")
            # 尝试降级方案
            return preload_fallback_standard_klines_fixed(C)

        # 步骤2: 使用与主策略相同的合成逻辑
        print(f"   🔄 使用主策略合成逻辑进行K线合成...")
        merged_klines = []
        kline_buffer = tick_data_list.copy()

        # 实现与主策略相同的非重叠合成模式（2根合成1根）
        merge_count = 0
        while len(kline_buffer) >= 2 and len(merged_klines) < required_klines:
            try:
                # 使用相同的merge_two_bars函数
                merged_kline = merge_two_bars(kline_buffer[:2])
                merged_klines.append(merged_kline)

                if merge_count < 3:  # 只显示前3个合成结果
                    print(f"      ✅ 合成K线{merge_count + 1}: OHLC[{merged_kline['open']}, {merged_kline['high']}, {merged_kline['low']}, {merged_kline['close']}]")

                # 移除已使用的数据（非重叠模式）
                kline_buffer = kline_buffer[2:]
                merge_count += 1

            except Exception as e:
                print(f"      ⚠️ 合成K线{merge_count + 1}失败: {e}")
                kline_buffer = kline_buffer[2:]  # 跳过有问题的数据
                continue

        print(f"   🔄 合成完成: {len(merged_klines)}个K线")

        # 步骤3: 数据质量检查
        valid_klines = []
        for kline in merged_klines:
            if (kline['high'] >= kline['close'] >= kline['low'] and
                kline['high'] >= kline['open'] >= kline['low'] and
                kline['volume'] >= 0):
                valid_klines.append(kline)

        print(f"   ✅ 数据质量检查: {len(valid_klines)}/{len(merged_klines)}个有效K线")

        # 步骤4: 检查是否满足最低要求
        min_required = max(C.ATRLength, C.VolLen)
        if len(valid_klines) < min_required:
            print(f"   ❌ 有效K线不足: {len(valid_klines)} < {min_required}")
            return preload_fallback_standard_klines_fixed(C)

        # 步骤5: 填充到策略的merged_klines数组
        C.merged_klines = valid_klines

        print(f"   🎉 预热数据加载成功!")
        print(f"      ✅ 加载K线数量: {len(C.merged_klines)}")
        print(f"      ✅ 数据来源: tick级合成数据")
        print(f"      ✅ 合成方法: 与实时数据相同")
        print(f"      ✅ 可立即开始交易")

        return True

    except Exception as e:
        print(f"   ❌ 预热数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return preload_fallback_standard_klines_fixed(C)

def preload_fallback_standard_klines_fixed(C):
    """
    降级方案：使用标准K线数据 - 修复版

    参数:
        C: 策略上下文

    返回:
        bool: 是否成功
    """
    print(f"   🔄 使用降级方案: 标准K线数据")

    try:
        required_klines = max(C.ATRLength, C.VolLen) + 10

        # 获取标准K线数据 - 使用参考文件的API格式
        historical_data = C.get_market_data_ex(
            ['open', 'high', 'low', 'close', 'volume'],  # 字段列表（数组格式）
            [C.stock],                                   # 股票列表（数组格式）
            period=C.period,
            count=required_klines,
            subscribe=False
        )

        if not historical_data or C.stock not in historical_data:
            print(f"      ❌ 标准K线数据获取失败")
            return False

        stock_data = historical_data[C.stock]

        # 使用参考文件的数据处理方式转换为合成K线格式
        opens = stock_data['open'].values.astype(float) if hasattr(stock_data['open'], 'values') else [float(x) for x in stock_data['open']]
        highs = stock_data['high'].values.astype(float) if hasattr(stock_data['high'], 'values') else [float(x) for x in stock_data['high']]
        lows = stock_data['low'].values.astype(float) if hasattr(stock_data['low'], 'values') else [float(x) for x in stock_data['low']]
        closes = stock_data['close'].values.astype(float) if hasattr(stock_data['close'], 'values') else [float(x) for x in stock_data['close']]
        volumes = stock_data['volume'].values.astype(float) if hasattr(stock_data['volume'], 'values') else [float(x) for x in stock_data['volume']]

        merged_klines = []
        for i in range(len(closes)):
            merged_kline = {
                'start_time': f"标准K线_{i}",
                'end_time': f"标准K线_{i}",
                'open': round(opens[i], 3),
                'high': round(highs[i], 3),
                'low': round(lows[i], 3),
                'close': round(closes[i], 3),
                'volume': volumes[i],
                'data_source': 'standard_kline'  # 添加数据源标识
            }
            merged_klines.append(merged_kline)

        # 填充到策略数组
        C.merged_klines = merged_klines

        print(f"      ⚠️ 降级方案完成: {len(merged_klines)}个标准K线")
        print(f"      ⚠️ 注意: 此数据与实时合成数据可能存在差异")
        print(f"      ⚠️ 建议: 监控技术指标的连续性")

        return True

    except Exception as e:
        print(f"      ❌ 降级方案也失败: {e}")
        return False

def validate_volume_continuity(C):
    """
    验证预热数据与实时数据的成交量连续性

    参数:
        C: 策略上下文

    返回:
        dict: 验证结果
    """
    try:
        if not C.merged_klines or len(C.merged_klines) == 0:
            return {'status': 'no_data', 'message': '无预热数据'}

        # 分析预热数据的成交量特征 - 增强版
        preload_volumes = []
        tick_merged_count = 0
        standard_count = 0
        skipped_first_tick_count = 0
        reliable_data_count = 0

        for kline in C.merged_klines:
            preload_volumes.append(kline['volume'])
            if 'start_time' in kline and 'tick' in kline.get('start_time', ''):
                tick_merged_count += 1
                # 检查是否跳过了第一个不可靠的tick数据
                if 'data_quality' in kline and kline['data_quality'] == 'reliable':
                    reliable_data_count += 1
                elif 'data_quality' in kline and kline['data_quality'] == 'skipped':
                    skipped_first_tick_count += 1
            elif kline.get('data_source') == 'standard_kline':
                standard_count += 1

        # 计算成交量统计
        total_volume = sum(preload_volumes)
        avg_volume = total_volume / len(preload_volumes) if preload_volumes else 0
        max_volume = max(preload_volumes) if preload_volumes else 0
        min_volume = min(preload_volumes) if preload_volumes else 0

        # 检查成交量异常
        zero_volume_count = sum(1 for v in preload_volumes if v <= 0)
        negative_volume_count = sum(1 for v in preload_volumes if v < 0)

        # 预热数据最后一个K线的成交量（用于与实时数据衔接）
        last_preload_volume = preload_volumes[-1] if preload_volumes else 0

        validation_result = {
            'status': 'success',
            'total_klines': len(C.merged_klines),
            'tick_merged_count': tick_merged_count,
            'standard_count': standard_count,
            'volume_stats': {
                'total': total_volume,
                'average': avg_volume,
                'max': max_volume,
                'min': min_volume,
                'last_preload': last_preload_volume
            },
            'volume_quality': {
                'zero_count': zero_volume_count,
                'negative_count': negative_volume_count,
                'quality_score': (len(preload_volumes) - zero_volume_count - negative_volume_count) / len(preload_volumes) * 100 if preload_volumes else 0,
                'skipped_first_tick_count': skipped_first_tick_count,
                'reliable_data_count': reliable_data_count
            },
            'data_consistency': 'excellent' if tick_merged_count > 0 else 'needs_monitoring' if standard_count > 0 else 'unknown',
            'first_tick_handling': 'improved' if skipped_first_tick_count > 0 else 'standard'
        }

        # 设置连续性检查的基准值
        C.last_preload_volume = last_preload_volume
        C.volume_validation_result = validation_result

        return validation_result

    except Exception as e:
        return {'status': 'error', 'message': f'验证失败: {e}'}

def estimate_cumulative_volume_from_preload(C):
    """
    从预热数据推算当前的累积成交量基准

    参数:
        C: 策略上下文

    返回:
        float: 推算的累积成交量基准
    """
    try:
        if not hasattr(C, 'merged_klines') or not C.merged_klines:
            return 0

        # 方案1：如果预热数据来自tick合成，尝试推算累积成交量
        if any('历史tick' in kline.get('start_time', '') for kline in C.merged_klines):
            # 预热数据是基于tick合成的，累加所有增量成交量作为基准
            total_incremental_volume = sum(kline['volume'] for kline in C.merged_klines)

            # 推算：假设这是最近一段时间的增量，累积基准应该更大
            # 使用保守的估算：增量总和的2-3倍作为累积基准
            estimated_cumulative = total_incremental_volume * 2.5

            print(f"      📊 推算累积成交量: 增量总和={total_incremental_volume:.0f}, 估算累积={estimated_cumulative:.0f}")
            return estimated_cumulative

        # 方案2：如果预热数据来自标准K线，使用最后一个K线的成交量
        elif any(kline.get('data_source') == 'standard_kline' for kline in C.merged_klines):
            last_volume = C.merged_klines[-1]['volume']
            # 标准K线的成交量可能已经是累积值，直接使用
            estimated_cumulative = last_volume * 10  # 保守估算

            print(f"      📊 推算累积成交量: 基于标准K线={last_volume:.0f}, 估算累积={estimated_cumulative:.0f}")
            return estimated_cumulative

        # 方案3：默认保守估算
        else:
            avg_volume = sum(kline['volume'] for kline in C.merged_klines) / len(C.merged_klines)
            estimated_cumulative = avg_volume * len(C.merged_klines) * 3

            print(f"      📊 推算累积成交量: 基于平均值={avg_volume:.0f}, 估算累积={estimated_cumulative:.0f}")
            return estimated_cumulative

    except Exception as e:
        print(f"      ⚠️ 累积成交量推算失败: {e}")
        return 0

def get_tick_level_historical_data(stock, period, required_klines):
    """
    获取tick级别的历史数据用于合成

    参数:
        stock: 股票代码
        period: 周期
        required_klines: 需要的合成K线数量

    返回:
        list: tick级别的数据列表
    """
    try:
        # 需要获取的tick数量 = 需要的K线数量 × 2（因为2个tick合成1个K线）
        required_ticks = required_klines * 2 + 20  # 多获取一些作为缓冲

        print(f"📊 获取tick级历史数据: {required_ticks}个数据点")

        # 方案1: 尝试获取更高频率的数据
        tick_data = None

        # 尝试获取tick数据（如果QMT支持）
        try:
            tick_data = get_market_data_ex(
                fields=['lastPrice', 'volume'],
                stock_list=[stock],
                period='tick',  # 尝试tick级数据
                count=required_ticks,
                subscribe=False
            )
        except:
            pass

        # 如果tick数据不可用，尝试获取更高频率的K线数据
        if not tick_data or stock not in tick_data:
            print("   ⚠️ tick数据不可用，尝试获取高频K线数据")

            # 根据目标周期选择更高频率的数据
            high_freq_period = get_higher_frequency_period(period)
            high_freq_count = required_ticks

            tick_data = get_market_data_ex(
                fields=['close', 'volume'],  # 使用收盘价作为tick价格
                stock_list=[stock],
                period=high_freq_period,
                count=high_freq_count,
                subscribe=False
            )

        if not tick_data or stock not in tick_data:
            print("   ❌ 无法获取高频历史数据")
            return []

        # 转换为tick格式
        stock_data = tick_data[stock]
        tick_list = []

        # 处理tick数据或高频K线数据
        if 'lastPrice' in stock_data:
            # 真实tick数据
            prices = stock_data['lastPrice']
            volumes = stock_data['volume']
        else:
            # 高频K线数据，使用收盘价作为tick价格
            prices = stock_data['close']
            volumes = stock_data['volume']

        # 转换为tick格式 - 直接使用增量成交量
        for i in range(len(prices)):
            current_volume = float(volumes[i])

            # 直接使用增量成交量 - tick数据确定为增量
            volume_increment = max(1, current_volume)  # 确保至少为1

            # 基本的异常值检测
            if current_volume > 100000:  # 如果单个tick成交量超过10万，可能异常
                volume_increment = min(current_volume, 10000)  # 限制在1万以内
                print(f"      ⚠️ 异常大成交量: {current_volume:.0f}, 修正为: {volume_increment:.0f}")
            elif current_volume <= 0:
                volume_increment = 1  # 最小保护
                print(f"      ⚠️ 零成交量, 修正为: {volume_increment:.0f}")

            # 创建tick数据点
            tick_data_point = {
                'time': f"历史tick_{i}",
                'price': float(prices[i]),
                'volume': volume_increment,
                'data_quality': 'reliable'
            }
            tick_list.append(tick_data_point)

        print(f"   ✅ 获取到{len(tick_list)}个tick数据点")
        return tick_list

    except Exception as e:
        print(f"   ❌ 获取tick级数据失败: {e}")
        return []

def preload_historical_data_with_merge(C):
    """
    使用与主策略相同的合成逻辑预加载历史数据

    参数:
        C: 策略上下文

    返回:
        bool: 是否成功预加载
    """
    print(f"\n🔥 开始预热数据加载 - 使用合成逻辑")
    print(f"   目标: {C.stock}, 周期: {C.period}")

    try:
        # 计算需要的K线数量
        required_klines = max(C.ATRLength, C.VolLen) + 10  # 多加载一些作为缓冲

        # 步骤1: 获取tick级别的历史数据
        tick_data_list = get_tick_level_historical_data(C.stock, C.period, required_klines)

        if len(tick_data_list) < required_klines:
            print(f"   ⚠️ tick数据不足: {len(tick_data_list)} < {required_klines}")
            # 尝试降级方案
            return preload_fallback_standard_klines(C)

        # 步骤2: 使用与主策略相同的合成逻辑
        print(f"   🔄 使用主策略合成逻辑进行K线合成...")
        merged_klines = []
        kline_buffer = tick_data_list.copy()

        # 实现与主策略相同的非重叠合成模式（2根合成1根）
        merge_count = 0
        while len(kline_buffer) >= 2 and len(merged_klines) < required_klines:
            try:
                # 使用相同的merge_two_bars函数
                merged_kline = merge_two_bars(kline_buffer[:2])
                merged_klines.append(merged_kline)

                if merge_count < 3:  # 只显示前3个合成结果
                    print(f"      ✅ 合成K线{merge_count + 1}: OHLC[{merged_kline['open']}, {merged_kline['high']}, {merged_kline['low']}, {merged_kline['close']}]")

                # 移除已使用的数据（非重叠模式）
                kline_buffer = kline_buffer[2:]
                merge_count += 1

            except Exception as e:
                print(f"      ⚠️ 合成K线{merge_count + 1}失败: {e}")
                kline_buffer = kline_buffer[2:]  # 跳过有问题的数据
                continue

        print(f"   🔄 合成完成: {len(merged_klines)}个K线")

        # 步骤3: 数据质量检查
        valid_klines = []
        for kline in merged_klines:
            if (kline['high'] >= kline['close'] >= kline['low'] and
                kline['high'] >= kline['open'] >= kline['low'] and
                kline['volume'] >= 0):
                valid_klines.append(kline)

        print(f"   ✅ 数据质量检查: {len(valid_klines)}/{len(merged_klines)}个有效K线")

        # 步骤4: 检查是否满足最低要求
        min_required = max(C.ATRLength, C.VolLen)
        if len(valid_klines) < min_required:
            print(f"   ❌ 有效K线不足: {len(valid_klines)} < {min_required}")
            return preload_fallback_standard_klines(C)

        # 步骤5: 填充到策略的merged_klines数组
        C.merged_klines = valid_klines

        print(f"   🎉 预热数据加载成功!")
        print(f"      ✅ 加载K线数量: {len(C.merged_klines)}")
        print(f"      ✅ 数据来源: tick级合成数据")
        print(f"      ✅ 合成方法: 与实时数据相同")
        print(f"      ✅ 可立即开始交易")

        return True

    except Exception as e:
        print(f"   ❌ 预热数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return preload_fallback_standard_klines(C)

def preload_fallback_standard_klines(C):
    """
    降级方案：使用标准K线数据

    参数:
        C: 策略上下文

    返回:
        bool: 是否成功
    """
    print(f"   🔄 使用降级方案: 标准K线数据")

    try:
        required_klines = max(C.ATRLength, C.VolLen) + 10

        # 获取标准K线数据
        historical_data = C.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume'],
            stock_list=[C.stock],
            period=C.period,
            count=required_klines,
            subscribe=False
        )

        if not historical_data or C.stock not in historical_data:
            print(f"      ❌ 标准K线数据获取失败")
            return False

        stock_data = historical_data[C.stock]

        # 转换为合成K线格式
        merged_klines = []
        for i in range(len(stock_data['close'])):
            merged_kline = {
                'start_time': f"标准K线_{i}",
                'end_time': f"标准K线_{i}",
                'open': round(float(stock_data['open'][i]), 3),
                'high': round(float(stock_data['high'][i]), 3),
                'low': round(float(stock_data['low'][i]), 3),
                'close': round(float(stock_data['close'][i]), 3),
                'volume': float(stock_data['volume'][i]),
                'data_source': 'standard_kline'  # 添加数据源标识
            }
            merged_klines.append(merged_kline)

        # 填充到策略数组
        C.merged_klines = merged_klines

        print(f"      ⚠️ 降级方案完成: {len(merged_klines)}个标准K线")
        print(f"      ⚠️ 注意: 此数据与实时合成数据可能存在差异")
        print(f"      ⚠️ 建议: 监控技术指标的连续性")

        return True

    except Exception as e:
        print(f"      ❌ 降级方案也失败: {e}")
        return False

# ============================================================================
# QMT内置函数说明
# ============================================================================
"""
实盘环境说明:
1. get_trade_detail_data() - QMT内置函数，用于获取账户和持仓信息
2. passorder() - QMT内置函数，用于提交交易订单
3. 以上函数在QMT实盘环境中自动可用，无需自定义实现

如果在非QMT环境中测试，请确保已正确安装和配置QMT相关组件
"""

# ============================================================================
# 策略监控和统计函数
# ============================================================================

def print_strategy_summary(C):
    """
    打印策略运行摘要 - 包含委托管理信息
    """
    print(f"\n📊 策略运行摘要:")
    print(f"   总交易次数: {C.total_trades}")
    print(f"   成功交易: {C.successful_trades}")
    print(f"   失败交易: {C.failed_trades}")
    if C.total_trades > 0:
        success_rate = C.successful_trades / C.total_trades * 100
        print(f"   成功率: {success_rate:.1f}%")

    # 委托管理状态
    pending_orders = [order for order in C.waiting_list if order['status'] == 'pending']
    print(f"   未确认委托: {len(pending_orders)}个")

    if pending_orders:
        for order in pending_orders:
            elapsed = (datetime.datetime.now() - order['submit_time']).total_seconds()
            print(f"     - {order['action']} {order['volume']}股 (等待{elapsed:.0f}秒)")

    if C.last_error:
        print(f"   最后错误: {C.last_error}")

def print_order_management_status(C):
    """
    打印委托管理状态
    """
    print(f"\n📋 委托管理状态:")
    print(f"   等待列表: {len(C.waiting_list)}个委托")
    print(f"   最大允许: {C.max_pending_orders}个")
    print(f"   下单冷却: {C.order_cooldown}秒")

    if C.last_order_time:
        elapsed = (datetime.datetime.now() - C.last_order_time).total_seconds()
        print(f"   上次下单: {elapsed:.0f}秒前")

    # 显示等待列表详情
    if C.waiting_list:
        print("   委托详情:")
        for i, order in enumerate(C.waiting_list[-3:]):  # 只显示最近3个
            elapsed = (datetime.datetime.now() - order['submit_time']).total_seconds()
            print(f"     {i+1}. {order['action']} {order['volume']}股 [{order['status']}] ({elapsed:.0f}秒前)")

"""
========================================================================
🚀 实盘部署完整指南:

📋 部署前检查清单:
1. ✅ 确认QMT软件正常运行且已连接交易服务器
2. ✅ 在QMT模型交易界面正确配置账户信息（系统会自动传递account和accountType）
3. ✅ 确认账户资金充足且在风险承受范围内
4. ✅ 检查目标股票的交易权限和可交易状态
5. ✅ 验证系统时间与交易所时间同步

⚙️ 关键参数调整:
- max_position_ratio: 最大仓位比例 (默认30%, 可根据风险偏好调整)
- min_cash_reserve: 最小资金保留 (默认1万, 确保流动性)
- ATRs: ATR止盈倍数 (默认16倍, 影响止盈幅度)
- MinATR: 最小ATR阈值 (已调整为0.02, 适合A股市场)

🛡️ 实盘安全机制:
- 多重数据有效性验证
- 完善的异常处理和错误日志
- 资金管理和风险控制
- 策略可在任何时间执行（已移除交易时间限制）
- 交易确认和状态同步

📊 监控要点:
- 关注"📤 提交订单"和"✅ 订单成功"日志
- 监控"💰 账户状态"中的资金和持仓变化
- 检查"🔍 开仓/平仓条件检查"的逻辑判断
- 观察"📊 策略状态"中的技术指标变化

⚠️ 重要提醒:
1. 首次使用建议小资金测试
2. 密切监控前几笔交易的执行情况
3. 如遇异常立即停止策略并检查日志
4. 定期检查持仓状态与策略状态是否同步

🔧 技术支持:
- 如遇"QMT函数未定义"错误，请检查QMT环境配置
- 如遇交易失败，请检查账户权限和网络连接
- 建议在QMT模拟环境中先行测试验证

⚠️ 免责声明: 本策略仅供学习研究，实盘交易风险自负

🔄 撤单功能说明:
- 策略已集成自动撤单功能，避免资金和持仓被未成交订单占用
- 买入前自动撤销之前的买入订单，释放冻结资金
- 卖出前自动撤销之前的卖出订单，释放冻结持仓
- 可通过 cancel_all_orders(C) 手动撤销所有订单
- 可通过 monitor_order_status(C) 监控当前订单状态

📋 撤单功能配置:
- C.auto_cancel_orders: 是否自动撤销未成交订单 (默认: True)
- C.cancel_before_new_order: 新订单前是否撤销旧订单 (默认: True)
- C.enable_order_monitoring: 是否启用订单状态监控 (默认: True)
- C.max_order_age: 订单最大存活时间，秒 (默认: 300)

========================================================================
"""

# ============================================================================
# QMT订单管理和撤单工具 - 修复实盘运行问题
# ============================================================================

def check_has_pending_orders(C, order_type='ALL'):
    """
    快速检查是否有未处理的订单 - 防止重复挂单的关键函数

    参数:
        C: 策略上下文
        order_type: 订单类型 ('BUY', 'SELL', 'ALL')

    返回:
        bool: True表示有未处理订单，False表示没有
    """
    try:
        # 方法1：使用get_last_order_id快速检查
        try:
            last_order_id = get_last_order_id(C.acct, 'STOCK', 'ORDER')
            if last_order_id and last_order_id != '-1':
                print(f"🔍 发现最新订单ID: {last_order_id}")
                # 检查订单状态
                order_info = check_specific_order_status(C, last_order_id)
                if order_info and order_info.get('is_pending', False):
                    print(f"⚠️ 最新订单仍在处理中")
                    return True
        except Exception as e:
            print(f"⚠️ 快速检查方法失败: {e}")

        # 方法2：查询所有委托（备用方法）
        try:
            orders = get_trade_detail_data(C.acct, 'stock', 'order')
            if not orders or len(orders) == 0:
                return False

            # 检查是否有未完成的订单
            for order_obj in orders:
                try:
                    order_status = getattr(order_obj, 'm_nOrderStatus', -1)
                    order_op_type = getattr(order_obj, 'm_nOffsetFlag', -1)
                    order_stock = getattr(order_obj, 'm_strInstrumentID', '') + '.' + getattr(order_obj, 'm_strExchangeID', '')

                    # 检查是否为当前股票的未完成订单
                    if order_stock == C.stock and order_status in [48, 49, 50, 51]:
                        # 检查订单类型
                        if order_type == 'ALL':
                            return True
                        elif order_type == 'BUY' and order_op_type == 1:
                            return True
                        elif order_type == 'SELL' and order_op_type == 2:
                            return True

                except Exception as e:
                    print(f"⚠️ 检查订单异常: {e}")
                    continue

        except Exception as e:
            print(f"⚠️ 查询委托异常: {e}")

        return False

    except Exception as e:
        print(f"❌ 检查未处理订单异常: {e}")
        return False  # 出错时返回False，允许继续执行

def check_specific_order_status(C, order_id):
    """
    检查特定订单的状态

    参数:
        C: 策略上下文
        order_id: 订单ID

    返回:
        dict: 订单状态信息
    """
    try:
        orders = get_trade_detail_data(C.acct, 'stock', 'order')
        if not orders:
            return None

        for order_obj in orders:
            try:
                obj_order_id = getattr(order_obj, 'm_strOrderSysID', '')
                if obj_order_id == order_id:
                    order_status = getattr(order_obj, 'm_nOrderStatus', -1)
                    return {
                        'order_id': order_id,
                        'status': order_status,
                        'is_pending': order_status in [48, 49, 50, 51],
                        'is_completed': order_status == 52,
                        'is_cancelled': order_status in [53, 54]
                    }
            except Exception as e:
                continue

        return None

    except Exception as e:
        print(f"❌ 检查订单状态异常: {e}")
        return None

def cancel_pending_orders(C, order_type='ALL', max_age_seconds=300, max_retries=3):
    """
    撤销未成交的挂单 - 改进版，解决实盘运行问题

    参数:
        C: 策略上下文
        order_type: 订单类型 ('BUY', 'SELL', 'ALL')
        max_age_seconds: 最大订单年龄（秒），超过此时间的订单将被撤销
        max_retries: 最大重试次数

    返回:
        dict: 撤单结果统计
    """
    cancel_stats = {
        'total_checked': 0,
        'cancelled_count': 0,
        'cancel_success': 0,
        'cancel_failed': 0,
        'errors': [],
        'has_pending_orders': False  # 新增：是否还有未处理订单
    }

    try:
        # 使用更可靠的方法获取委托信息
        for retry in range(max_retries):
            try:
                print(f"🔄 第{retry+1}次尝试查询委托信息...")
                orders = get_trade_detail_data(C.acct, 'stock', 'order')

                if orders is None:
                    print(f"⚠️ 第{retry+1}次查询返回None，等待重试...")
                    if retry < max_retries - 1:
                        import time
                        time.sleep(1)
                        continue
                    else:
                        print("❌ 多次查询失败，跳过撤单")
                        return cancel_stats

                if len(orders) == 0:
                    print("📋 没有找到待处理的委托")
                    return cancel_stats

                print(f"📋 查询到 {len(orders)} 个委托")
                cancel_stats['total_checked'] = len(orders)
                break

            except Exception as e:
                print(f"❌ 第{retry+1}次查询委托异常: {e}")
                if retry < max_retries - 1:
                    import time
                    time.sleep(1)
                    continue
                else:
                    cancel_stats['errors'].append(f"查询委托失败: {e}")
                    return cancel_stats

        # 遍历所有委托，找到需要撤销的
        try:
            for order_obj in orders:
                try:
                    # 获取委托信息
                    order_status = getattr(order_obj, 'm_nOrderStatus', -1)
                    order_op_type = getattr(order_obj, 'm_nOffsetFlag', -1)  # 买卖方向
                    order_stock = getattr(order_obj, 'm_strInstrumentID', '') + '.' + getattr(order_obj, 'm_strExchangeID', '')
                    order_volume = getattr(order_obj, 'm_nVolumeTotalOriginal', 0)
                    order_id = getattr(order_obj, 'm_strOrderSysID', '')  # 委托号

                    print(f"📋 检查委托: ID={order_id}, 状态={order_status}, 方向={order_op_type}, 股票={order_stock}")

                    # 判断是否需要撤销
                    should_cancel = False
                    cancel_reason = ""

                    # 检查委托状态（根据QMT官方文档）
                    # 48=未报, 49=待报, 50=已报待成交, 51=部分成交
                    if order_status in [48, 49, 50]:
                        should_cancel = True
                        cancel_reason = f"委托状态={order_status}(未成交)"
                    elif order_status == 51:
                        should_cancel = True
                        cancel_reason = f"委托状态={order_status}(部分成交)"

                    # 检查委托类型过滤
                    if should_cancel and order_type != 'ALL':
                        # m_nOffsetFlag: 1=买入, 2=卖出（根据官方文档）
                        if order_type == 'BUY' and order_op_type != 1:
                            should_cancel = False
                        elif order_type == 'SELL' and order_op_type != 2:
                            should_cancel = False

                    # 检查是否为当前股票
                    if should_cancel and order_stock != C.stock:
                        should_cancel = False
                        cancel_reason += "(非当前股票)"

                    if should_cancel and order_id and order_id != '':
                        cancel_stats['cancelled_count'] += 1
                        print(f"🔄 准备撤销委托: {cancel_reason}")

                        # 执行撤单（使用QMT官方cancel函数）
                        try:
                            cancel_result = cancel(order_id, C.acct, 'STOCK', C)
                            if cancel_result:
                                cancel_stats['cancel_success'] += 1
                                print(f"✅ 撤单成功: ID={order_id}")
                            else:
                                cancel_stats['cancel_failed'] += 1
                                print(f"❌ 撤单失败: ID={order_id}")
                        except Exception as e:
                            cancel_stats['cancel_failed'] += 1
                            cancel_stats['errors'].append(f"撤单异常: {e}")
                            print(f"❌ 撤单异常: {e}")
                    else:
                        if order_status not in [48, 49, 50, 51]:
                            print(f"📋 委托无需撤销: 状态={order_status}")

                except Exception as e:
                    cancel_stats['errors'].append(f"处理委托异常: {e}")
                    print(f"❌ 处理委托异常: {e}")

        except NameError:
            print("⚠️ QMT环境未就绪，无法查询委托信息")
            return cancel_stats
        except Exception as e:
            cancel_stats['errors'].append(f"查询委托异常: {e}")
            print(f"❌ 查询委托异常: {e}")

    except Exception as e:
        cancel_stats['errors'].append(f"撤单流程异常: {e}")
        print(f"❌ 撤单流程异常: {e}")

    return cancel_stats

def check_order_status(C, order_id=None):
    """
    检查委托状态 - 基于QMT官方API

    参数:
        C: 策略上下文
        order_id: 委托ID，如果为None则查询所有委托

    返回:
        dict: 委托状态信息
    """
    try:
        # 使用get_trade_detail_data获取委托信息
        try:
            orders = get_trade_detail_data(C.acct, 'stock', 'order')
            if not orders or len(orders) == 0:
                return {'status': 'no_order', 'message': '没有找到委托'}

            # 如果指定了order_id，查找特定委托
            if order_id is not None:
                target_order = None
                for order_obj in orders:
                    if getattr(order_obj, 'm_strOrderSysID', '') == order_id:
                        target_order = order_obj
                        break

                if not target_order:
                    return {'status': 'error', 'message': f'未找到委托ID: {order_id}'}

                orders = [target_order]  # 只处理指定的委托

            # 处理委托信息（取最新的一个）
            order_obj = orders[-1] if orders else None
            if not order_obj:
                return {'status': 'error', 'message': '委托对象为空'}

            order_status = getattr(order_obj, 'm_nOrderStatus', -1)
            order_op_type = getattr(order_obj, 'm_nOffsetFlag', -1)  # 买卖方向
            order_stock = getattr(order_obj, 'm_strInstrumentID', '') + '.' + getattr(order_obj, 'm_strExchangeID', '')
            order_volume = getattr(order_obj, 'm_nVolumeTotalOriginal', 0)
            traded_volume = getattr(order_obj, 'm_nVolumeTraded', 0)
            order_sys_id = getattr(order_obj, 'm_strOrderSysID', '')

            status_desc = {
                48: '未报',
                49: '待报',
                50: '已报待成交',
                51: '部分成交',
                52: '全部成交',
                53: '已撤销',
                54: '部分撤销'
            }.get(order_status, f'未知状态({order_status})')

            op_type_desc = {
                1: '买入',
                2: '卖出'
            }.get(order_op_type, f'未知方向({order_op_type})')

            return {
                'status': 'success',
                'order_id': order_sys_id,
                'order_status': order_status,
                'status_desc': status_desc,
                'op_type': order_op_type,
                'op_type_desc': op_type_desc,
                'stock': order_stock,
                'total_volume': order_volume,
                'traded_volume': traded_volume,
                'is_pending': order_status in [48, 49, 50, 51],  # 未完成状态
                'is_completed': order_status == 52,  # 全部成交
                'is_cancelled': order_status in [53, 54]  # 已撤销
            }

        except NameError:
            return {'status': 'error', 'message': 'QMT环境未就绪，无法查询委托'}
        except Exception as e:
            return {'status': 'error', 'message': f'查询委托异常: {e}'}

    except Exception as e:
        return {'status': 'error', 'message': f'查询异常: {e}'}

# ============================================================================
# QMT下单状态检测和调试工具
# ============================================================================

def test_passorder_return_values():
    """
    测试和调试QMT passorder函数的返回值
    用于排查下单状态检测问题
    """
    print("🔍 QMT passorder返回值说明:")
    print("   成功情况:")
    print("     - 返回订单ID（正整数，如: 12345）")
    print("     - 返回订单ID（字符串，如: 'ORD_20231201_001'）")
    print("     - 返回True（某些版本）")
    print("   失败情况:")
    print("     - 返回-1（下单失败）")
    print("     - 返回0（参数错误）")
    print("     - 返回None（系统错误）")
    print("     - 返回False（某些版本）")
    print("   判断逻辑: result is not None and result != -1 and result != 0 and result != False")

# validate_order_success函数已移除
# 根据QMT官方文档，passorder函数返回None，无需验证返回值

# ============================================================================
# 事件驱动回调函数 - 基于官方示例最佳实践
# ============================================================================

def order_callback(ContextInfo, orderInfo):
    """
    订单状态变化回调 - 替代复杂的委托管理

    优势：
    1. 自动处理订单状态变化
    2. 无需手动跟踪委托状态
    3. QMT平台保证可靠性

    参数：
        ContextInfo: QMT上下文对象
        orderInfo: 委托信息对象
    """
    try:
        stock = orderInfo.m_strInstrumentID + '.' + orderInfo.m_strExchangeID
        status = orderInfo.m_nOrderStatus
        volume = orderInfo.m_nVolumeTotalOriginal

        print(f"📋 订单状态更新: {stock} {volume}股 状态={status}")

        # 根据订单状态进行相应处理
        if status == 3:  # 全部成交
            print(f"✅ 订单完全成交: {stock}")
        elif status == 5:  # 部分成交
            print(f"🔄 订单部分成交: {stock}")
        elif status == 6:  # 已撤销
            print(f"❌ 订单已撤销: {stock}")
        elif status == 8:  # 已拒绝
            print(f"❌ 订单被拒绝: {stock}")

    except Exception as e:
        print(f"❌ 订单回调处理错误: {e}")

def deal_callback(ContextInfo, dealInfo):
    """
    成交回调 - 自动处理成交信息

    优势：
    1. 实时获取成交信息
    2. 自动计算成交均价
    3. 无需手动查询成交状态

    参数：
        ContextInfo: QMT上下文对象
        dealInfo: 成交信息对象
    """
    try:
        stock = dealInfo.m_strInstrumentID + '.' + dealInfo.m_strExchangeID
        volume = dealInfo.m_nVolume
        price = dealInfo.m_dPrice
        amount = dealInfo.m_dTradeAmount

        print(f"💰 成交通知: {stock} {volume}股 @{price:.3f} 金额:{amount:.2f}")

    except Exception as e:
        print(f"❌ 成交回调处理错误: {e}")

def orderError_callback(ContextInfo, passOrderInfo, msg):
    """
    下单错误回调 - 自动处理下单错误

    优势：
    1. 实时获取下单错误信息
    2. 便于调试和问题诊断
    3. 可实现自动重试逻辑

    参数：
        ContextInfo: QMT上下文对象
        passOrderInfo: 下单信息对象
        msg: 错误信息字符串
    """
    try:
        print(f"❌ 下单失败: {passOrderInfo.orderCode}")
        print(f"   错误信息: {msg}")

        # 可以在这里实现重试逻辑或其他错误处理

    except Exception as e:
        print(f"❌ 错误回调处理异常: {e}")

# ============================================================================
# 背离策略配置和监控函数
# ============================================================================

def update_divergence_strategy_config(C, enable=None, min_strength=None, weight=None):
    """
    动态更新背离策略配置

    参数:
        C: 策略上下文
        enable: 是否启用背离策略
        min_strength: 最小信号强度 ('weak', 'medium', 'strong')
        weight: 背离信号权重
    """
    if enable is not None:
        C.enable_divergence_strategy = enable
        print(f"✅ 背离策略开关已设置为: {enable}")

    if min_strength is not None:
        C.divergence_min_strength = min_strength
        print(f"✅ 最小信号强度已设置为: {min_strength}")

    if weight is not None:
        C.divergence_weight = weight
        print(f"✅ 背离信号权重已设置为: {weight}")

def print_divergence_strategy_status(C):
    """
    打印CMF+BIAS双重背离策略状态
    """
    print(f"\n📊 CMF+BIAS双重背离策略状态:")
    print(f"   启用状态: {C.enable_divergence_strategy}")
    print(f"   SKDJ参数: N={C.SKDJ_N}, M={C.SKDJ_M}")
    print(f"   CMF参数: N={C.CMF_N}, M={C.CMF_M}")
    print(f"   BIAS参数: N={C.BIAS_N}, M={C.BIAS_M}")
    print(f"   ADX参数: N={C.ADX_N}, M={C.ADX_M}")
    print(f"   VAE参数: 基础TR={C.VAE_基础TR}%, 初始止损={C.VAE_初始止损}%, 周期={C.VAE_周期}")
    print(f"   固定止损: {C.固定止损}%")

    if hasattr(C, 'divergence_detector') and C.divergence_detector:
        print(f"   检测器状态: 已初始化")
    else:
        print(f"   检测器状态: 未初始化")

def print_divergence_signals_summary(C, indicators):
    """
    打印CMF+BIAS双重背离信号摘要

    参数:
        C: 策略上下文
        indicators: 技术指标字典
    """
    if not indicators.get('divergence'):
        return

    divergence_result = indicators['divergence']

    print(f"\n📊 CMF+BIAS双重背离信号摘要:")
    print(f"   检测状态: {divergence_result['status']}")

    if divergence_result['status'] == 'success':
        buy_signal = divergence_result.get('buy_signal', False)
        sell_signal = divergence_result.get('sell_signal', False)
        conditions = divergence_result.get('conditions', {})
        indicators_data = divergence_result.get('indicators', {})
        VAE_info = divergence_result.get('VAE_info', {})
        data_quality = divergence_result.get('data_quality', {})

        print(f"   数据质量: {data_quality.get('total_bars', 0)}根K线")
        print(f"   数据覆盖率: {data_quality.get('data_coverage', 0):.1%}")

        print(f"   🎯 买入信号: {buy_signal}")
        print(f"   📉 卖出信号: {sell_signal}")

        print(f"   📊 SKDJ超卖: {conditions.get('SKDJ超卖', False)} (K={indicators_data.get('SKDJ_K', 0):.1f}, D={indicators_data.get('SKDJ_D', 0):.1f})")
        print(f"   💰 CMF底背离: {conditions.get('CMF底背离', False)} (CMF={indicators_data.get('CMF', 0):.3f})")
        print(f"   📈 BIAS底背离: {conditions.get('BIAS底背离', False)} (BIAS={indicators_data.get('BIAS', 0):.2f}%)")
        print(f"   🔄 双重背离: {conditions.get('双重背离', False)}")
        print(f"   💪 强趋势确认: {conditions.get('强趋势确认', False)} (ADX={indicators_data.get('ADX', 0):.1f})")
        print(f"   🚀 突破确认: {conditions.get('突破确认', False)}")

        if VAE_info:
            波动率区间 = VAE_info.get('波动率区间', '未知')
            实际波动幅度 = VAE_info.get('实际波动幅度', 0)
            动态止盈比例 = VAE_info.get('动态止盈比例', 0)
            动态止损比例 = VAE_info.get('动态止损比例', 0)
            动态TR = VAE_info.get('动态TR', 0)

            # 显示修复后的VAE计算详情
            计算止盈比例 = VAE_info.get('计算止盈比例', 0)
            计算止损比例 = VAE_info.get('计算止损比例', 0)
            基础止盈 = VAE_info.get('基础止盈', 0)
            基础止损 = VAE_info.get('基础止损', 0)
            止盈止损比例 = VAE_info.get('止盈止损比例', 0)

            # 显示ATR优化信息
            短期ATR周期 = VAE_info.get('短期ATR周期', 0)
            长期ATR周期 = VAE_info.get('长期ATR周期', 0)
            ATR优化 = VAE_info.get('ATR优化', '未知')

            print(f"   🌊 VAE风控: {波动率区间} (市场波动={实际波动幅度:.2f}%)")
            print(f"   🎯 动态止盈: {动态止盈比例:.2f}% | 动态止损: {动态止损比例:.2f}% (比例={止盈止损比例:.1f})")
            print(f"   🔧 计算详情: 计算值({计算止盈比例:.2f}%/{计算止损比例:.2f}%) vs 基础值({基础止盈:.1f}%/{基础止损:.1f}%)")
            print(f"   📊 ATR优化: {ATR优化} (短期{短期ATR周期}期/长期{长期ATR周期}期)")
            print(f"   📋 传统逻辑: 动态TR={动态TR:.1f}% (仅作对比)")
    else:
        print(f"   状态说明: {divergence_result.get('error_message', '数据不足或计算失败')}")
